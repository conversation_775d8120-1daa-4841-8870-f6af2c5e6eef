<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地质剖面报表生成测试（批量处理）</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 2px dashed #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        button {
            background: #4CAF50;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background: #45a049;
        }
        button:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .download-link {
            display: inline-block;
            margin-top: 10px;
            padding: 8px 16px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 3px;
        }
        .download-link:hover {
            background: #0056b3;
        }
        .info {
            background: #e2e3e5;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>地质剖面报表生成测试（支持批量处理）</h1>

        <div class="info">
            <strong>使用说明（支持批量处理）：</strong><br>
            1. 选择一个或多个地质剖面图片文件（支持 jpg、png、gif 等格式）<br>
            2. 点击"生成报表"按钮<br>
            3. 点击“生成报表”按钮<br>
            4. 处理完成后可下载生成的Excel文件
        </div>

        <form id="uploadForm">
            <div class="form-group">
                <label for="fileInput">选择地质剖面图片（支持多选）：</label>
                <input type="file" id="fileInput" name="files" accept="image/*" multiple required>
            </div>

            <div id="fileInfo" style="margin-bottom: 10px; color: #666; font-size: 14px;"></div>

            <button type="submit" id="submitBtn">生成报表</button>
        </form>

        <div id="result" class="result"></div>
    </div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const fileInput = document.getElementById('fileInput');
            const submitBtn = document.getElementById('submitBtn');
            const resultDiv = document.getElementById('result');

            if (!fileInput.files || fileInput.files.length === 0) {
                showResult('请选择要上传的图片文件', 'error');
                return;
            }

            // 显示加载状态
            submitBtn.disabled = true;
            const fileCount = fileInput.files.length;
            submitBtn.textContent = `正在处理${fileCount}个文件...`;
            showResult(`正在批量处理${fileCount}个图片文件并生成报表，请稍候...`, 'loading');

            try {
                const formData = new FormData();
                for (let i = 0; i < fileInput.files.length; i++) {
                    formData.append('files', fileInput.files[i]);
                }

                const response = await fetch('/api/simple-report/generate', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    const message = `
                        <strong>报表生成成功！</strong><br>
                        处理文件数: ${result.file_count}<br>
                        文件名: ${result.filenames}<br>
                        <a href="${result.report_url}" class="download-link" target="_blank">下载Excel报表</a>
                    `;
                    showResult(message, 'success');
                } else {
                    showResult(`生成失败: ${result.message}`, 'error');
                }

            } catch (error) {
                console.error('Error:', error);
                showResult(`请求失败: ${error.message}`, 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '生成报表';
            }
        });

        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }

        // 文件选择时显示文件信息
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const files = e.target.files;
            if (files && files.length > 0) {
                let totalSize = 0;
                let fileNames = [];
                for (let i = 0; i < files.length; i++) {
                    totalSize += files[i].size;
                    fileNames.push(files[i].name);
                }
                const totalSizeMB = (totalSize / 1024 / 1024).toFixed(2);
                console.log(`选择了${files.length}个文件: [${fileNames.join(', ')}], 总大小: ${totalSizeMB}MB`);

                // 在页面上显示文件信息
                const fileInfo = document.getElementById('fileInfo');
                if (fileInfo) {
                    fileInfo.textContent = `已选择${files.length}个文件，总大小: ${totalSizeMB}MB`;
                }
            }
        });
    </script>
</body>
</html>
