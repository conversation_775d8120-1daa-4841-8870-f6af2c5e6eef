spring.application.name=generate_report

# Server Configuration
server.port=8080
server.servlet.context-path=/

# Python API Configuration
python.api.base-url=http://localhost:8000
python.api.timeout=300

# File Upload Configuration
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB

# Logging Configuration
logging.level.com.bluesheep.generate_report=INFO
logging.level.org.springframework.web.reactive.function.client=DEBUG

# Jackson Configuration
spring.jackson.default-property-inclusion=NON_NULL
spring.jackson.serialization.write-dates-as-timestamps=false
