package com.bluesheep.generate_report.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 剖面处理响应数据模型
 * 对应Python接口 /section-unified/process 的返回结果
 */
@Data
public class SectionProcessResponse {
    
    private Boolean success;
    
    @JsonProperty("file_id")
    private String fileId;
    
    private String filename;
    
    @JsonProperty("image_width")
    private Integer imageWidth;
    
    @JsonProperty("image_height")
    private Integer imageHeight;
    
    private List<SimpleSection> sections;
    
    private Summary summary;
    
    private String timestamp;
    
    @JsonProperty("json_file_url")
    private String jsonFileUrl;
    
    @Data
    public static class SimpleSection {
        @JsonProperty("section_id")
        private String sectionId;
        
        private Map<String, Object> bbox;
        
        @JsonProperty("bbox_relative")
        private Map<String, Object> bboxRelative;
        
        @JsonProperty("drill_holes")
        private List<SimpleDrillHole> drillHoles;
        
        private List<Spacing> spacings;
    }
    
    @Data
    public static class SimpleDrillHole {
        @JsonProperty("hole_id")
        private String holeId;
        
        private Map<String, Object> bbox;
        
        @JsonProperty("bbox_relative")
        private Map<String, Object> bboxRelative;
        
        private List<SoilSampleInfo> samples;
        
        @JsonProperty("depth_elevation_pairs")
        private List<DepthElevationPair> depthElevationPairs;
    }
    
    @Data
    public static class SoilSampleInfo {
        private Map<String, Object> bbox;
        
        @JsonProperty("bbox_relative")
        private Map<String, Object> bboxRelative;
        
        private Double depth;
        
        private Double elevation;
        
        @JsonProperty("sample_type")
        private String sampleType;
        
        private Double confidence;
    }
    
    @Data
    public static class DepthElevationPair {
        private Double depth;
        private Double elevation;
    }
    
    @Data
    public static class Spacing {
        @JsonProperty("spacing_id")
        private String spacingId;
        
        private Map<String, Object> bbox;
        
        @JsonProperty("bbox_relative")
        private Map<String, Object> bboxRelative;
        
        private String value;
        
        private Double confidence;
    }
    
    @Data
    public static class Summary {
        @JsonProperty("total_sections")
        private Integer totalSections;
        
        @JsonProperty("total_drill_holes")
        private Integer totalDrillHoles;
        
        @JsonProperty("total_spacings")
        private Integer totalSpacings;
        
        @JsonProperty("total_soil_samples")
        private Integer totalSoilSamples;
    }
}
