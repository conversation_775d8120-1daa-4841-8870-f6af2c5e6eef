package com.bluesheep.generate_report.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 报表生成请求数据模型
 * 对应Python接口 /api/reports/generate 的请求参数
 */
@Data
public class ReportGenerateRequest {
    
    @JsonProperty("template_name")
    private String templateName;
    
    @JsonProperty("output_filename")
    private String outputFilename;
    
    @JsonProperty("data_source")
    private String dataSource = "manual";
    
    @JsonProperty("data_source_config")
    private Map<String, Object> dataSourceConfig;
    
    private List<Sheet> sheets;
    
    @JsonProperty("global_styles")
    private GlobalStyles globalStyles;
    
    private Metadata metadata;
    
    @Data
    public static class Sheet {
        private String name;
        private List<Map<String, Object>> data;
        private List<String> headers;
        
        @JsonProperty("merge_cells")
        private List<MergeCell> mergeCells;
        
        @JsonProperty("freeze_panes")
        private String freezePanes;
        
        @JsonProperty("column_widths")
        private Map<String, Integer> columnWidths;
        
        @JsonProperty("row_heights")
        private Map<Integer, Integer> rowHeights;
        
        private Map<String, CellStyle> styles;
        
        @JsonProperty("start_row")
        private Integer startRow;
        
        @JsonProperty("start_col")
        private Integer startCol;
    }
    
    @Data
    public static class MergeCell {
        private String range;
        private String value;
        private CellStyle style;
    }
    
    @Data
    public static class CellStyle {
        @JsonProperty("font_name")
        private String fontName;
        
        @JsonProperty("font_size")
        private Integer fontSize;
        
        @JsonProperty("font_bold")
        private Boolean fontBold;
        
        @JsonProperty("font_color")
        private String fontColor;
        
        @JsonProperty("background_color")
        private String backgroundColor;
        
        @JsonProperty("horizontal_align")
        private String horizontalAlign;
        
        @JsonProperty("vertical_align")
        private String verticalAlign;
        
        private Boolean border;
    }
    
    @Data
    public static class GlobalStyles {
        @JsonProperty("font_name")
        private String fontName;
        
        @JsonProperty("font_size")
        private Integer fontSize;
        
        private Boolean border;
        
        @JsonProperty("horizontal_align")
        private String horizontalAlign;
    }
    
    @Data
    public static class Metadata {
        @JsonProperty("report_type")
        private String reportType;
        
        @JsonProperty("created_by")
        private String createdBy;
        
        private String description;
        
        private String version;
    }
}
