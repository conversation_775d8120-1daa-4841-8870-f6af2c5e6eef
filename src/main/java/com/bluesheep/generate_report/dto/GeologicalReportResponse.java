package com.bluesheep.generate_report.dto;

import lombok.Data;

/**
 * 地质报表生成响应数据模型
 */
@Data
public class GeologicalReportResponse {
    
    private Boolean success;
    
    private String taskId;
    
    private String message;
    
    private String fileId;
    
    private String reportUrl;
    
    private SectionProcessResponse.Summary processSummary;
    
    public static GeologicalReportResponse success(String taskId, String fileId, String message) {
        GeologicalReportResponse response = new GeologicalReportResponse();
        response.setSuccess(true);
        response.setTaskId(taskId);
        response.setFileId(fileId);
        response.setMessage(message);
        return response;
    }
    
    public static GeologicalReportResponse error(String message) {
        GeologicalReportResponse response = new GeologicalReportResponse();
        response.setSuccess(false);
        response.setMessage(message);
        return response;
    }
}
