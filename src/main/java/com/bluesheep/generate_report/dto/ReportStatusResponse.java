package com.bluesheep.generate_report.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 报表状态查询响应数据模型
 * 对应Python接口 /api/reports/{task_id}/status 的返回结果
 */
@Data
public class ReportStatusResponse {
    
    private String status;
    
    private Integer progress;
    
    @JsonProperty("result_file")
    private String resultFile;
    
    @JsonProperty("error_message")
    private String errorMessage;
    
    private String message;
}
