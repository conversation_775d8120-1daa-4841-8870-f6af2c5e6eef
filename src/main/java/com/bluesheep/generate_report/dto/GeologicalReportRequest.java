package com.bluesheep.generate_report.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.constraints.NotNull;

/**
 * 地质报表生成请求数据模型
 * 整合剖面处理和报表生成的完整流程
 */
@Data
public class GeologicalReportRequest {
    
    @NotNull(message = "图片文件不能为空")
    private MultipartFile imageFile;
    
    // 剖面处理参数
    private Double detectionConfidence = 0.5;
    private Double detectionIou = 0.5;
    private Double detailConfidence = 0.5;
    private Double detailIou = 0.5;
    private String detailModelType = "section_detail_recognition";
    private String ocrModel = "rapid";
    
    // 土样检测参数
    private Boolean enableSoilSampleDetection = true;
    private Double soilDetectionConfidence = 0.5;
    private Double soilDetectionIou = 0.5;
    
    // 报表生成参数
    private String outputFilename = "地质剖面孔位统计表.xlsx";
    private String reportTitle = "地质剖面孔位统计表";
}
