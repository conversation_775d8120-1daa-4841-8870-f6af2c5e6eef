package com.bluesheep.generate_report.service;

import com.bluesheep.generate_report.dto.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.MediaType;
import org.springframework.http.client.MultipartBodyBuilder;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.time.Duration;
import java.util.*;

/**
 * 地质报表服务
 * 负责调用Python接口进行剖面处理和报表生成
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GeologicalReportService {
    
    private final WebClient webClient;
    
    /**
     * 处理地质剖面图片
     * 调用Python接口 /section-unified/process
     */
    public Mono<SectionProcessResponse> processSectionImage(GeologicalReportRequest request) {
        log.info("开始处理地质剖面图片: {}", request.getImageFile().getOriginalFilename());
        
        try {
            MultipartBodyBuilder builder = new MultipartBodyBuilder();
            
            // 添加文件
            builder.part("file", new ByteArrayResource(request.getImageFile().getBytes()) {
                @Override
                public String getFilename() {
                    return request.getImageFile().getOriginalFilename();
                }
            }).header("Content-Disposition", 
                     "form-data; name=file; filename=" + request.getImageFile().getOriginalFilename());
            
            // 添加处理参数
            builder.part("detection_confidence", request.getDetectionConfidence());
            builder.part("detection_iou", request.getDetectionIou());
            builder.part("detail_confidence", request.getDetailConfidence());
            builder.part("detail_iou", request.getDetailIou());
            builder.part("detail_model_type", request.getDetailModelType());
            builder.part("ocr_model", request.getOcrModel());
            builder.part("enable_soil_sample_detection", request.getEnableSoilSampleDetection());
            builder.part("soil_detection_confidence", request.getSoilDetectionConfidence());
            builder.part("soil_detection_iou", request.getSoilDetectionIou());
            builder.part("return_image", false);
            builder.part("skip_detail_recognition", false);
            builder.part("skip_soil_sample_detection", false);
            
            return webClient.post()
                    .uri("/section-unified/process")
                    .contentType(MediaType.MULTIPART_FORM_DATA)
                    .body(BodyInserters.fromMultipartData(builder.build()))
                    .retrieve()
                    .bodyToMono(SectionProcessResponse.class)
                    .timeout(Duration.ofMinutes(5))
                    .doOnSuccess(response -> log.info("剖面处理完成: 检测到{}个剖面, {}个钻孔", 
                            response.getSummary().getTotalSections(),
                            response.getSummary().getTotalDrillHoles()))
                    .doOnError(error -> log.error("剖面处理失败", error));
                    
        } catch (IOException e) {
            log.error("读取图片文件失败", e);
            return Mono.error(new RuntimeException("读取图片文件失败", e));
        }
    }
    
    /**
     * 生成报表
     * 调用Python接口 /api/reports/generate
     */
    public Mono<ReportGenerateResponse> generateReport(SectionProcessResponse sectionData, GeologicalReportRequest request) {
        log.info("开始生成报表: {}", request.getOutputFilename());
        
        ReportGenerateRequest reportRequest = buildReportRequest(sectionData, request);
        
        return webClient.post()
                .uri("/api/reports/generate")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(reportRequest)
                .retrieve()
                .bodyToMono(ReportGenerateResponse.class)
                .timeout(Duration.ofMinutes(2))
                .doOnSuccess(response -> log.info("报表生成任务已提交, 任务ID: {}", response.getTaskId()))
                .doOnError(error -> log.error("报表生成失败", error));
    }
    
    /**
     * 查询报表生成状态
     * 调用Python接口 /api/reports/{task_id}/status
     */
    public Mono<ReportStatusResponse> getReportStatus(String taskId) {
        return webClient.get()
                .uri("/api/reports/{taskId}/status", taskId)
                .retrieve()
                .bodyToMono(ReportStatusResponse.class)
                .timeout(Duration.ofSeconds(30));
    }
    
    /**
     * 轮询等待报表生成完成
     */
    public Mono<ReportStatusResponse> waitForReportCompletion(String taskId) {
        return Mono.defer(() -> getReportStatus(taskId))
                .flatMap(status -> {
                    if ("completed".equals(status.getStatus())) {
                        log.info("报表生成完成: {}", status.getResultFile());
                        return Mono.just(status);
                    } else if ("failed".equals(status.getStatus())) {
                        log.error("报表生成失败: {}", status.getErrorMessage());
                        return Mono.error(new RuntimeException("报表生成失败: " + status.getErrorMessage()));
                    } else {
                        log.info("报表生成中... 进度: {}%", status.getProgress());
                        return Mono.delay(Duration.ofSeconds(2))
                                .then(waitForReportCompletion(taskId));
                    }
                })
                .timeout(Duration.ofMinutes(10));
    }
    
    /**
     * 完整的地质报表生成流程
     */
    public Mono<GeologicalReportResponse> generateGeologicalReport(GeologicalReportRequest request) {
        return processSectionImage(request)
                .flatMap(sectionData -> {
                    return generateReport(sectionData, request)
                            .flatMap(reportResponse -> {
                                return waitForReportCompletion(reportResponse.getTaskId())
                                        .map(statusResponse -> {
                                            GeologicalReportResponse response = GeologicalReportResponse.success(
                                                    reportResponse.getTaskId(),
                                                    sectionData.getFileId(),
                                                    "地质报表生成成功"
                                            );
                                            response.setReportUrl(statusResponse.getResultFile());
                                            response.setProcessSummary(sectionData.getSummary());
                                            return response;
                                        });
                            });
                })
                .onErrorMap(error -> {
                    log.error("地质报表生成失败", error);
                    return new RuntimeException("地质报表生成失败: " + error.getMessage(), error);
                });
    }
    
    /**
     * 构建报表生成请求
     */
    private ReportGenerateRequest buildReportRequest(SectionProcessResponse sectionData, GeologicalReportRequest request) {
        ReportGenerateRequest reportRequest = new ReportGenerateRequest();
        reportRequest.setOutputFilename(request.getOutputFilename());
        reportRequest.setDataSource("manual");
        reportRequest.setDataSourceConfig(new HashMap<>());
        
        // 构建工作表数据
        List<ReportGenerateRequest.Sheet> sheets = new ArrayList<>();
        ReportGenerateRequest.Sheet sheet = new ReportGenerateRequest.Sheet();
        sheet.setName("剖面孔位统计");
        
        // 转换剖面数据为报表数据
        List<Map<String, Object>> data = convertSectionDataToReportData(sectionData);
        sheet.setData(data);
        
        // 设置表头
        sheet.setHeaders(Arrays.asList("剖面号", "孔号1", "孔距1", "孔号2", "孔距2", "孔号3", "孔距3", 
                                      "孔号4", "孔距4", "孔号5", "孔距5", "备注"));
        
        // 设置合并单元格
        List<ReportGenerateRequest.MergeCell> mergeCells = new ArrayList<>();
        ReportGenerateRequest.MergeCell titleCell = new ReportGenerateRequest.MergeCell();
        titleCell.setRange("A1:L1");
        titleCell.setValue(request.getReportTitle());
        
        ReportGenerateRequest.CellStyle titleStyle = new ReportGenerateRequest.CellStyle();
        titleStyle.setFontName("微软雅黑");
        titleStyle.setFontSize(16);
        titleStyle.setFontBold(true);
        titleStyle.setHorizontalAlign("center");
        titleStyle.setVerticalAlign("center");
        titleStyle.setBackgroundColor("#4CAF50");
        titleStyle.setFontColor("#FFFFFF");
        titleCell.setStyle(titleStyle);
        
        mergeCells.add(titleCell);
        sheet.setMergeCells(mergeCells);
        
        // 设置其他格式
        sheet.setFreezePanes("A3");
        
        Map<String, Integer> columnWidths = new HashMap<>();
        columnWidths.put("A", 12);
        for (char col = 'B'; col <= 'K'; col++) {
            columnWidths.put(String.valueOf(col), 8);
        }
        columnWidths.put("L", 12);
        sheet.setColumnWidths(columnWidths);
        
        Map<Integer, Integer> rowHeights = new HashMap<>();
        rowHeights.put(1, 30);
        rowHeights.put(2, 25);
        sheet.setRowHeights(rowHeights);
        
        // 设置样式
        Map<String, ReportGenerateRequest.CellStyle> styles = new HashMap<>();
        
        ReportGenerateRequest.CellStyle headerStyle = new ReportGenerateRequest.CellStyle();
        headerStyle.setFontBold(true);
        headerStyle.setBackgroundColor("#E8F5E8");
        headerStyle.setBorder(true);
        headerStyle.setHorizontalAlign("center");
        headerStyle.setVerticalAlign("center");
        styles.put("A2:L2", headerStyle);
        
        ReportGenerateRequest.CellStyle dataStyle = new ReportGenerateRequest.CellStyle();
        dataStyle.setBorder(true);
        dataStyle.setHorizontalAlign("center");
        dataStyle.setVerticalAlign("center");
        styles.put("A3:L" + (data.size() + 2), dataStyle);
        
        sheet.setStyles(styles);
        sheet.setStartRow(2);
        sheet.setStartCol(1);
        
        sheets.add(sheet);
        reportRequest.setSheets(sheets);
        
        // 设置全局样式
        ReportGenerateRequest.GlobalStyles globalStyles = new ReportGenerateRequest.GlobalStyles();
        globalStyles.setFontName("微软雅黑");
        globalStyles.setFontSize(11);
        globalStyles.setBorder(true);
        globalStyles.setHorizontalAlign("center");
        reportRequest.setGlobalStyles(globalStyles);
        
        // 设置元数据
        ReportGenerateRequest.Metadata metadata = new ReportGenerateRequest.Metadata();
        metadata.setReportType("geological_profile");
        metadata.setCreatedBy("地质勘探系统");
        metadata.setDescription("地质剖面孔位统计报表");
        metadata.setVersion("1.0");
        reportRequest.setMetadata(metadata);
        
        return reportRequest;
    }
    
    /**
     * 将剖面数据转换为报表数据
     */
    private List<Map<String, Object>> convertSectionDataToReportData(SectionProcessResponse sectionData) {
        List<Map<String, Object>> data = new ArrayList<>();
        
        if (sectionData.getSections() != null) {
            for (SectionProcessResponse.SimpleSection section : sectionData.getSections()) {
                Map<String, Object> row = new HashMap<>();
                row.put("剖面号", section.getSectionId());
                
                // 处理钻孔数据
                if (section.getDrillHoles() != null && !section.getDrillHoles().isEmpty()) {
                    for (int i = 0; i < Math.min(5, section.getDrillHoles().size()); i++) {
                        SectionProcessResponse.SimpleDrillHole hole = section.getDrillHoles().get(i);
                        row.put("孔号" + (i + 1), hole.getHoleId());
                        
                        // 计算孔距（这里简化处理，实际可能需要更复杂的计算）
                        if (i < section.getSpacings().size()) {
                            row.put("孔距" + (i + 1), section.getSpacings().get(i).getValue());
                        } else {
                            row.put("孔距" + (i + 1), "——");
                        }
                    }
                    
                    // 填充空白列
                    for (int i = section.getDrillHoles().size(); i < 5; i++) {
                        row.put("孔号" + (i + 1), "");
                        row.put("孔距" + (i + 1), "");
                    }
                } else {
                    // 如果没有钻孔数据，填充空白
                    for (int i = 1; i <= 5; i++) {
                        row.put("孔号" + i, "");
                        row.put("孔距" + i, "");
                    }
                }
                
                row.put("备注", "");
                data.add(row);
            }
        }
        
        return data;
    }
}
