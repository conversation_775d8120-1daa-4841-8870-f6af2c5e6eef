package com.bluesheep.generate_report.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.MediaType;
import org.springframework.http.client.MultipartBodyBuilder;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;

import java.io.IOException;
import java.time.Duration;
import java.util.*;

/**
 * 简化版报表服务
 * 直接调用两个Python接口生成Excel报表
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SimpleReportService {

    private final WebClient webClient;

    /**
     * 生成地质剖面报表 - 支持多张图片批量处理
     * 直接调用两个接口并返回Excel文件URL
     */
    public String generateReport(MultipartFile[] imageFiles) {
        try {
            log.info("开始批量处理地质剖面图片，共 {} 张", imageFiles.length);

            // 第一步：调用批量剖面处理接口
            Map<String, Object> sectionData = processSectionImagesBatch(imageFiles);
            log.info("批量剖面处理完成，检测到 {} 个剖面", getSectionCount(sectionData));

            // 第二步：动态生成报表配置
            Map<String, Object> reportConfig = buildDynamicReportConfig(sectionData);

            // 第三步：调用报表生成接口
            String taskId = generateExcelReport(reportConfig);
            log.info("报表生成任务已提交，任务ID: {}", taskId);

            // 第四步：等待报表生成完成
            String reportUrl = waitForReportCompletion(taskId);
            log.info("报表生成完成: {}", reportUrl);

            return reportUrl;

        } catch (Exception e) {
            log.error("生成报表失败", e);
            throw new RuntimeException("生成报表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 兼容单张图片的方法
     */
    public String generateReport(MultipartFile imageFile) {
        return generateReport(new MultipartFile[]{imageFile});
    }


    /**
     * 调用批量剖面处理接口
     */
    private Map<String, Object> processSectionImagesBatch(MultipartFile[] imageFiles) throws IOException {
        MultipartBodyBuilder builder = new MultipartBodyBuilder();

        // 添加多个文件
        for (MultipartFile imageFile : imageFiles) {
            builder.part("files", new ByteArrayResource(imageFile.getBytes()) {
                @Override
                public String getFilename() {
                    return imageFile.getOriginalFilename();
                }
            }).header("Content-Disposition",
                    "form-data; name=files; filename=" + imageFile.getOriginalFilename());
        }

        // 添加默认参数
        builder.part("detection_confidence", 0.5);
        builder.part("detection_iou", 0.5);
        builder.part("detail_confidence", 0.5);
        builder.part("detail_iou", 0.5);
        builder.part("detail_model_type", "section_detail_recognition");
        builder.part("ocr_model", "rapid");
        builder.part("enable_soil_sample_detection", true);
        builder.part("soil_detection_confidence", 0.5);
        builder.part("soil_detection_iou", 0.5);
        builder.part("return_image", false);
        builder.part("skip_detail_recognition", false);
        builder.part("skip_soil_sample_detection", false);
        builder.part("max_workers", 3);
        builder.part("timeout_per_file", 300);

        return webClient.post()
                .uri("/section-unified/process-batch")
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .body(BodyInserters.fromMultipartData(builder.build()))
                .retrieve()
                .bodyToMono(Map.class)
                .timeout(Duration.ofMinutes(10))
                .block();
    }

    /**
     * 调用单张剖面处理接口（兼容旧版本）
     */
    private Map<String, Object> processSectionImage(MultipartFile imageFile) throws IOException {
        MultipartBodyBuilder builder = new MultipartBodyBuilder();

        // 添加文件
        builder.part("file", new ByteArrayResource(imageFile.getBytes()) {
            @Override
            public String getFilename() {
                return imageFile.getOriginalFilename();
            }
        }).header("Content-Disposition",
                "form-data; name=file; filename=" + imageFile.getOriginalFilename());

        // 添加默认参数
        builder.part("detection_confidence", 0.5);
        builder.part("detection_iou", 0.5);
        builder.part("detail_confidence", 0.5);
        builder.part("detail_iou", 0.5);
        builder.part("detail_model_type", "section_detail_recognition");
        builder.part("ocr_model", "rapid");
        builder.part("enable_soil_sample_detection", true);
        builder.part("soil_detection_confidence", 0.5);
        builder.part("soil_detection_iou", 0.5);
        builder.part("return_image", false);
        builder.part("skip_detail_recognition", false);
        builder.part("skip_soil_sample_detection", false);

        return webClient.post()
                .uri("http://localhost:8000/section-unified/process")
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .body(BodyInserters.fromMultipartData(builder.build()))
                .retrieve()
                .bodyToMono(Map.class)
                .timeout(Duration.ofMinutes(5))
                .block();
    }

    /**
     * 动态构建报表配置（根据JSON数据确定列数和行数）
     */
    private Map<String, Object> buildDynamicReportConfig(Map<String, Object> sectionData) {
        Map<String, Object> config = new HashMap<>();
        config.put("template_name", null);
        config.put("output_filename", "地质剖面孔位统计表.xlsx");
        config.put("data_source", "manual");
        config.put("data_source_config", new HashMap<>());

        // 分析JSON数据获取最大钻孔数和剖面数
        int maxDrillHoles = getMaxDrillHolesCount(sectionData);
        int sectionCount = getSectionCount(sectionData);

        log.info("动态报表配置: 最大钻孔数={}, 剖面数={}", maxDrillHoles, sectionCount);

        // 构建工作表
        List<Map<String, Object>> sheets = new ArrayList<>();
        Map<String, Object> sheet = new HashMap<>();
        sheet.put("name", "剖面孔位统计");

        // 转换剖面数据为表格数据
        List<Map<String, Object>> data = convertSectionDataToTableData(sectionData);
        sheet.put("data", data);

        // 动态生成表头
        List<String> headers = generateDynamicHeaders(maxDrillHoles);
        sheet.put("headers", headers);

        // 计算列数（剖面号 + 钻孔对数 * 2 + 备注）
        int totalColumns = 1 + maxDrillHoles * 2 + 1;
        String lastColumn = getColumnLetter(totalColumns);

        // 设置合并单元格
        List<Map<String, Object>> mergeCells = new ArrayList<>();
        Map<String, Object> titleCell = new HashMap<>();
        titleCell.put("range", "A1:" + lastColumn + "1");
        titleCell.put("value", "地质剖面孔位统计表");

        Map<String, Object> titleStyle = new HashMap<>();
        titleStyle.put("font_name", "微软雅黑");
        titleStyle.put("font_size", 16);
        titleStyle.put("font_bold", true);
        titleStyle.put("horizontal_align", "center");
        titleStyle.put("vertical_align", "center");
        titleStyle.put("background_color", "#4CAF50");
        titleStyle.put("font_color", "#FFFFFF");
        titleCell.put("style", titleStyle);

        mergeCells.add(titleCell);
        sheet.put("merge_cells", mergeCells);

        // 设置其他格式
        sheet.put("freeze_panes", "A3");

        // 动态设置列宽
        Map<String, Integer> columnWidths = generateDynamicColumnWidths(totalColumns);
        sheet.put("column_widths", columnWidths);

        Map<String, Integer> rowHeights = new HashMap<>();
        rowHeights.put("1", 30);
        rowHeights.put("2", 25);
        sheet.put("row_heights", rowHeights);

        // 动态设置样式
        Map<String, Map<String, Object>> styles = generateDynamicStyles(totalColumns, data.size());
        sheet.put("styles", styles);

        sheet.put("start_row", 2);
        sheet.put("start_col", 1);

        sheets.add(sheet);
        config.put("sheets", sheets);

        // 设置全局样式
        Map<String, Object> globalStyles = new HashMap<>();
        globalStyles.put("font_name", "微软雅黑");
        globalStyles.put("font_size", 11);
        globalStyles.put("border", true);
        globalStyles.put("horizontal_align", "center");
        config.put("global_styles", globalStyles);

        // 设置元数据
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("report_type", "geological_profile");
        metadata.put("created_by", "地质勘探系统");
        metadata.put("description", "地质剖面孔位统计报表");
        metadata.put("version", "1.0");
        metadata.put("max_drill_holes", maxDrillHoles);
        metadata.put("section_count", sectionCount);
        config.put("metadata", metadata);

        return config;
    }

    /**
     * 构建报表配置（旧版本，兼容性保留）
     */
    private Map<String, Object> buildReportConfig(Map<String, Object> sectionData) {
        Map<String, Object> config = new HashMap<>();
        config.put("template_name", null);
        config.put("output_filename", "地质剖面孔位统计表.xlsx");
        config.put("data_source", "manual");
        config.put("data_source_config", new HashMap<>());

        // 构建工作表
        List<Map<String, Object>> sheets = new ArrayList<>();
        Map<String, Object> sheet = new HashMap<>();
        sheet.put("name", "剖面孔位统计");

        // 转换剖面数据为表格数据
        List<Map<String, Object>> data = convertSectionDataToTableData(sectionData);
        sheet.put("data", data);

        // 设置表头
        sheet.put("headers", Arrays.asList("剖面号", "孔号1", "孔距1", "孔号2", "孔距2",
                "孔号3", "孔距3", "孔号4", "孔距4", "孔号5", "孔距5", "备注"));

        // 设置合并单元格
        List<Map<String, Object>> mergeCells = new ArrayList<>();
        Map<String, Object> titleCell = new HashMap<>();
        titleCell.put("range", "A1:L1");
        titleCell.put("value", "地质剖面孔位统计表");

        Map<String, Object> titleStyle = new HashMap<>();
        titleStyle.put("font_name", "微软雅黑");
        titleStyle.put("font_size", 16);
        titleStyle.put("font_bold", true);
        titleStyle.put("horizontal_align", "center");
        titleStyle.put("vertical_align", "center");
        titleStyle.put("background_color", "#4CAF50");
        titleStyle.put("font_color", "#FFFFFF");
        titleCell.put("style", titleStyle);

        mergeCells.add(titleCell);
        sheet.put("merge_cells", mergeCells);

        // 设置其他格式
        sheet.put("freeze_panes", "A3");

        Map<String, Integer> columnWidths = new HashMap<>();
        columnWidths.put("A", 12);
        for (char col = 'B'; col <= 'K'; col++) {
            columnWidths.put(String.valueOf(col), 8);
        }
        columnWidths.put("L", 12);
        sheet.put("column_widths", columnWidths);

        Map<String, Integer> rowHeights = new HashMap<>();
        rowHeights.put("1", 30);
        rowHeights.put("2", 25);
        sheet.put("row_heights", rowHeights);

        // 设置样式
        Map<String, Map<String, Object>> styles = new HashMap<>();

        Map<String, Object> headerStyle = new HashMap<>();
        headerStyle.put("font_bold", true);
        headerStyle.put("background_color", "#E8F5E8");
        headerStyle.put("border", true);
        headerStyle.put("horizontal_align", "center");
        headerStyle.put("vertical_align", "center");
        styles.put("A2:L2", headerStyle);

        Map<String, Object> dataStyle = new HashMap<>();
        dataStyle.put("border", true);
        dataStyle.put("horizontal_align", "center");
        dataStyle.put("vertical_align", "center");
        styles.put("A3:L" + (data.size() + 2), dataStyle);

        Map<String, Object> leftAlignStyle = new HashMap<>();
        leftAlignStyle.put("horizontal_align", "left");
        styles.put("A3:A" + (data.size() + 2), leftAlignStyle);
        styles.put("L3:L" + (data.size() + 2), leftAlignStyle);

        sheet.put("styles", styles);
        sheet.put("start_row", 2);
        sheet.put("start_col", 1);

        sheets.add(sheet);
        config.put("sheets", sheets);

        // 设置全局样式
        Map<String, Object> globalStyles = new HashMap<>();
        globalStyles.put("font_name", "微软雅黑");
        globalStyles.put("font_size", 11);
        globalStyles.put("border", true);
        globalStyles.put("horizontal_align", "center");
        config.put("global_styles", globalStyles);

        // 设置元数据
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("report_type", "geological_profile");
        metadata.put("created_by", "地质勘探系统");
        metadata.put("description", "地质剖面孔位统计报表");
        metadata.put("version", "1.0");
        config.put("metadata", metadata);

        return config;
    }

    /**
     * 调用报表生成接口
     */
    private String generateExcelReport(Map<String, Object> reportConfig) {
        Map<String, Object> response = webClient.post()
                .uri("http://localhost:8001/api/reports/generate")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(reportConfig)
                .retrieve()
                .bodyToMono(Map.class)
                .timeout(Duration.ofMinutes(2))
                .block();

        return (String) response.get("task_id");
    }

    /**
     * 等待报表生成完成
     */
    private String waitForReportCompletion(String taskId) {
        int maxAttempts = 150; // 最多等待5分钟（150次 * 2秒）
        int attempts = 0;

        while (attempts < maxAttempts) {
            try {
                Map<String, Object> status = webClient.get()
                        .uri("http://localhost:8001/api/reports/{taskId}/status", taskId)
                        .retrieve()
                        .bodyToMono(Map.class)
                        .timeout(Duration.ofSeconds(30))
                        .block();

                String statusValue = (String) status.get("status");
                Integer progress = (Integer) status.get("progress");

                if ("completed".equals(statusValue)) {
                    return (String) status.get("result_file");
                } else if ("failed".equals(statusValue)) {
                    String errorMessage = (String) status.get("error_message");
                    throw new RuntimeException("报表生成失败: " + errorMessage);
                } else {
                    log.info("报表生成中... 进度: {}%", progress);
                    Thread.sleep(2000); // 等待2秒
                    attempts++;
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("等待报表生成被中断", e);
            }
        }

        throw new RuntimeException("报表生成超时");
    }

    /**
     * 将剖面数据转换为表格数据（支持动态列数）
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> convertSectionDataToTableData(Map<String, Object> sectionData) {
        List<Map<String, Object>> tableData = new ArrayList<>();

        // 获取最大钻孔数以确定列数
        int maxDrillHoles = getMaxDrillHolesCount(sectionData);

        List<Map<String, Object>> results = (List<Map<String, Object>>) sectionData.get("results");

        if (results != null) {
            for (Map<String, Object> reuslt : results) {

                Map<String, Object> singleResult = (Map<String, Object>) reuslt.get("result");

                List<Map<String, Object>> sections = (List<Map<String, Object>>) singleResult.get("sections");

                for (Map<String, Object> section : sections) {
                    // 如果有多个剖面，可能需要处理每个剖面的数据

                    Map<String, Object> row = new HashMap<>();

                    if (section.get("section_id") != null) {
                        row.put("section_id", section.get("section_id"));
                    } else {
                        log.warn("剖面数据中未找到section_id，使用默认值");
                        singleResult.put("section_id", "未知剖面");
                    }

                    List<Map<String, Object>> drillHoles = (List<Map<String, Object>>) section.get("drill_holes");
                    List<Map<String, Object>> spacings = (List<Map<String, Object>>) section.get("spacings");

                    if (drillHoles != null && !drillHoles.isEmpty()) {
                        for (int i = 0; i < Math.min(maxDrillHoles, drillHoles.size()); i++) {
                            Map<String, Object> hole = drillHoles.get(i);
                            row.put("孔号" + (i + 1), hole.get("hole_id"));

                            // 设置孔距
                            if (spacings != null && i < spacings.size()) {
                                row.put("孔距" + (i + 1), spacings.get(i).get("value"));
                            } else {
                                row.put("孔距" + (i + 1), "——");
                            }
                        }
                    }

                    // 处理钻孔逻辑
                    tableData.add(row);
                }


                // 处理钻孔数据
//            List<Map<String, Object>> drillHoles = (List<Map<String, Object>>) reuslt.get("drill_holes");
//            List<Map<String, Object>> spacings = (List<Map<String, Object>>) reuslt.get("spacings");
//
//            if (drillHoles != null && !drillHoles.isEmpty()) {
//                // 根据实际钻孔数填充数据
//                for (int i = 0; i < Math.min(maxDrillHoles, drillHoles.size()); i++) {
//                    Map<String, Object> hole = drillHoles.get(i);
//                    row.put("孔号" + (i + 1), hole.get("hole_id"));
//
//                    // 设置孔距
//                    if (spacings != null && i < spacings.size()) {
//                        row.put("孔距" + (i + 1), spacings.get(i).get("value"));
//                    } else {
//                        row.put("孔距" + (i + 1), "——");
//                    }
//                }
//
//                // 填充空白列（如果当前剖面的钻孔数少于最大钻孔数）
//                for (int i = drillHoles.size(); i < maxDrillHoles; i++) {
//                    row.put("孔号" + (i + 1), "");
//                    row.put("孔距" + (i + 1), "");
//                }
//            } else {
//                // 如果没有钻孔数据，填充空白
//                for (int i = 1; i <= maxDrillHoles; i++) {
//                    row.put("孔号" + i, "");
//                    row.put("孔距" + i, "");
//                }
//            }
//
//            row.put("备注", "");
//            tableData.add(row);
            }
        }

        return tableData;
    }

    /**
     * 获取剖面数量
     */
    @SuppressWarnings("unchecked")
    private int getSectionCount(Map<String, Object> sectionData) {
        Map<String, Object> summary = (Map<String, Object>) sectionData.get("summary");
        if (summary != null) {
            Integer totalSections = (Integer) summary.get("total_sections");
            return totalSections != null ? totalSections : 0;
        }
        return 0;
    }

    /**
     * 获取最大钻孔数量
     */
    @SuppressWarnings("unchecked")
    private int getMaxDrillHolesCount(Map<String, Object> data) {
        int maxDrillHoles = 0;

        List<Map<String, Object>> results = (List<Map<String, Object>>) data.get("results");
        if (results != null) {
            for (Map<String, Object> result : results) {
                Map<String, Object> singleResult = (Map<String, Object>) result.get("result");
                Map<String, Object> summary = (Map<String, Object>) singleResult.get("summary");
                if (summary != null) {
                    Integer drillHoles = (Integer) summary.get("total_drill_holes");
                    if (drillHoles != null && drillHoles > maxDrillHoles) {
                        maxDrillHoles = drillHoles;
                    }
                } else {
                    log.warn("剖面结果中未找到summary信息，无法获取最大钻孔数");
                }
            }
        }

        log.info("检测到最大钻孔数: {}", maxDrillHoles);
        return maxDrillHoles;
    }

    /**
     * 动态生成表头
     */
    private List<String> generateDynamicHeaders(int maxDrillHoles) {
        List<String> headers = new ArrayList<>();
        headers.add("剖面号");

        for (int i = 1; i <= maxDrillHoles; i++) {
            headers.add("孔号");
            headers.add("孔距");
        }

        headers.add("备注");

        log.info("动态生成表头: {}", headers);
        return headers;
    }

    /**
     * 动态生成列宽配置
     */
    private Map<String, Integer> generateDynamicColumnWidths(int totalColumns) {
        Map<String, Integer> columnWidths = new HashMap<>();

        // 第一列（剖面号）较宽
        columnWidths.put("A", 12);

        // 中间列（孔号和孔距）
        for (int i = 2; i < totalColumns; i++) {
            columnWidths.put(getColumnLetter(i), 8);
        }

        // 最后一列（备注）较宽
        columnWidths.put(getColumnLetter(totalColumns), 12);

        return columnWidths;
    }

    /**
     * 动态生成样式配置
     */
    private Map<String, Map<String, Object>> generateDynamicStyles(int totalColumns, int dataRows) {
        Map<String, Map<String, Object>> styles = new HashMap<>();

        String lastColumn = getColumnLetter(totalColumns);

        // 表头样式
        Map<String, Object> headerStyle = new HashMap<>();
        headerStyle.put("font_bold", true);
        headerStyle.put("background_color", "#E8F5E8");
        headerStyle.put("border", true);
        headerStyle.put("horizontal_align", "center");
        headerStyle.put("vertical_align", "center");
        styles.put("A2:" + lastColumn + "2", headerStyle);

        // 数据区域样式
        Map<String, Object> dataStyle = new HashMap<>();
        dataStyle.put("border", true);
        dataStyle.put("horizontal_align", "center");
        dataStyle.put("vertical_align", "center");
        styles.put("A3:" + lastColumn + (dataRows + 2), dataStyle);

        // 第一列和最后一列左对齐
        Map<String, Object> leftAlignStyle = new HashMap<>();
        leftAlignStyle.put("horizontal_align", "left");
        styles.put("A3:A" + (dataRows + 2), leftAlignStyle);
        styles.put(lastColumn + "3:" + lastColumn + (dataRows + 2), leftAlignStyle);

        return styles;
    }

    /**
     * 获取Excel列字母（A, B, C, ..., Z, AA, AB, ...）
     */
    private String getColumnLetter(int columnNumber) {
        StringBuilder result = new StringBuilder();
        while (columnNumber > 0) {
            columnNumber--; // 调整为0基索引
            result.insert(0, (char) ('A' + columnNumber % 26));
            columnNumber /= 26;
        }
        return result.toString();
    }
}
