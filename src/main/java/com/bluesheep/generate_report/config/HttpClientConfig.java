package com.bluesheep.generate_report.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;

import java.time.Duration;

/**
 * HTTP客户端配置
 * 用于调用Python API
 */
@Configuration
public class HttpClientConfig {
    
    @Value("${python.api.base-url:http://localhost:8000}")
    private String pythonApiBaseUrl;
    
    @Value("${python.api.timeout:300}")
    private int timeoutSeconds;
    
    @Bean
    public WebClient webClient() {
        HttpClient httpClient = HttpClient.create()
                .responseTimeout(Duration.ofSeconds(timeoutSeconds));
        
        return WebClient.builder()
                .baseUrl(pythonApiBaseUrl)
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .build();
    }
}
