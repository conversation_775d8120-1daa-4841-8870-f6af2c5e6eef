package com.bluesheep.generate_report.controller;

import com.bluesheep.generate_report.service.SimpleReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * 简化版报表控制器
 * 提供简单的地质报表生成接口
 */
@Slf4j
@RestController
@RequestMapping("/api/simple-report")
@RequiredArgsConstructor
public class SimpleReportController {

    private final SimpleReportService simpleReportService;

    /**
     * 生成地质剖面报表 - 支持多张图片批量处理
     * 直接上传图片，返回Excel报表下载链接
     */
    @PostMapping(value = "/generate", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<Map<String, Object>> generateReport(
            @RequestParam("files") MultipartFile[] files) {

        Map<String, Object> response = new HashMap<>();

        try {
            // 验证文件
            if (files == null || files.length == 0) {
                response.put("success", false);
                response.put("message", "请选择要上传的图片文件");
                return ResponseEntity.badRequest().body(response);
            }

            // 验证所有文件
            for (MultipartFile file : files) {
                if (file.isEmpty()) {
                    response.put("success", false);
                    response.put("message", "存在空文件，请检查上传的文件");
                    return ResponseEntity.badRequest().body(response);
                }

                // 验证文件类型
                String contentType = file.getContentType();
                if (contentType == null || !contentType.startsWith("image/")) {
                    response.put("success", false);
                    response.put("message", "请上传图片文件（jpg、png、gif等格式）: " + file.getOriginalFilename());
                    return ResponseEntity.badRequest().body(response);
                }
            }

            // 记录文件信息
            StringBuilder fileNames = new StringBuilder();
            long totalSize = 0;
            for (MultipartFile file : files) {
                if (fileNames.length() > 0) fileNames.append(", ");
                fileNames.append(file.getOriginalFilename());
                totalSize += file.getSize();
            }

            log.info("收到批量报表生成请求，文件数: {}, 文件名: [{}], 总大小: {} bytes",
                    files.length, fileNames.toString(), totalSize);

            // 生成报表
            String reportUrl = simpleReportService.generateReport(files);

            response.put("success", true);
            response.put("message", "报表生成成功");
            response.put("report_url", reportUrl);
            response.put("file_count", files.length);
            response.put("filenames", fileNames.toString());

            log.info("报表生成成功: {}", reportUrl);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("报表生成失败", e);
            response.put("success", false);
            response.put("message", "报表生成失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 生成地质剖面报表 - 单文件版本（兼容性）
     * 直接上传单张图片，返回Excel报表下载链接
     */
    @PostMapping(value = "/generate-single", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<Map<String, Object>> generateReportSingle(
            @RequestParam("file") MultipartFile file) {

        // 转换为数组并调用批量处理方法
        return generateReport(new MultipartFile[]{file});
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "Simple Report Service");
        response.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(response);
    }

    /**
     * 获取接口使用说明
     */
    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> info() {
        Map<String, Object> response = new HashMap<>();
        response.put("service", "地质剖面报表生成服务");
        response.put("version", "2.0.0");
        response.put("description", "上传地质剖面图片，自动生成Excel统计报表（支持多文件批量处理）");

        Map<String, Object> batchUsage = new HashMap<>();
        batchUsage.put("endpoint", "POST /api/simple-report/generate");
        batchUsage.put("method", "POST");
        batchUsage.put("content_type", "multipart/form-data");
        batchUsage.put("parameter", "files - 多个地质剖面图片文件");
        batchUsage.put("supported_formats", "jpg, jpeg, png, gif, bmp");
        batchUsage.put("max_file_size", "50MB");
        batchUsage.put("features", "动态列数、批量处理、智能表格生成");

        Map<String, Object> singleUsage = new HashMap<>();
        singleUsage.put("endpoint", "POST /api/simple-report/generate-single");
        singleUsage.put("method", "POST");
        singleUsage.put("content_type", "multipart/form-data");
        singleUsage.put("parameter", "file - 单个地质剖面图片文件");
        singleUsage.put("description", "兼容性接口，支持单文件上传");

        Map<String, Object> responseFormat = new HashMap<>();
        responseFormat.put("success", "boolean - 是否成功");
        responseFormat.put("message", "string - 响应消息");
        responseFormat.put("report_url", "string - Excel报表下载链接（成功时）");
        responseFormat.put("file_count", "integer - 处理的文件数量（成功时）");
        responseFormat.put("filenames", "string - 原始文件名列表（成功时）");

        response.put("batch_usage", batchUsage);
        response.put("single_usage", singleUsage);
        response.put("response_format", responseFormat);

        return ResponseEntity.ok(response);
    }
}