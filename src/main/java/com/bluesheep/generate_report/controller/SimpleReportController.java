package com.bluesheep.generate_report.controller;

import com.bluesheep.generate_report.service.SimpleReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * 简化版报表控制器
 * 提供简单的地质报表生成接口
 */
@Slf4j
@RestController
@RequestMapping("/api/simple-report")
@RequiredArgsConstructor
public class SimpleReportController {
    
    private final SimpleReportService simpleReportService;
    
    /**
     * 生成地质剖面报表 - 简化版本
     * 直接上传图片，返回Excel报表下载链接
     */
    @PostMapping(value = "/generate", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<Map<String, Object>> generateReport(
            @RequestParam("files") MultipartFile file) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 验证文件
            if (file.isEmpty()) {
                response.put("success", false);
                response.put("message", "请选择要上传的图片文件");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 验证文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                response.put("success", false);
                response.put("message", "请上传图片文件（jpg、png、gif等格式）");
                return ResponseEntity.badRequest().body(response);
            }
            
            log.info("收到报表生成请求，文件名: {}, 大小: {} bytes", 
                    file.getOriginalFilename(), file.getSize());
            
            // 生成报表
            String reportUrl = simpleReportService.generateReport(file);
            
            response.put("success", true);
            response.put("message", "报表生成成功");
            response.put("report_url", reportUrl);
            response.put("filename", file.getOriginalFilename());
            
            log.info("报表生成成功: {}", reportUrl);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("报表生成失败", e);
            response.put("success", false);
            response.put("message", "报表生成失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

}
