package com.bluesheep.generate_report.controller;

import com.bluesheep.generate_report.dto.*;
import com.bluesheep.generate_report.service.GeologicalReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import jakarta.validation.Valid;

/**
 * 地质报表控制器
 * 提供地质剖面处理和报表生成的REST API
 */
@Slf4j
@RestController
@RequestMapping("/api/geological-report")
@RequiredArgsConstructor
public class GeologicalReportController {
    
    private final GeologicalReportService geologicalReportService;
    
    /**
     * 生成地质报表
     * 完整流程：上传图片 -> 剖面处理 -> 生成报表
     */
    @PostMapping(value = "/generate", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Mono<ResponseEntity<GeologicalReportResponse>> generateReport(
            @Valid @ModelAttribute GeologicalReportRequest request) {
        
        log.info("收到地质报表生成请求: {}", request.getImageFile().getOriginalFilename());
        
        return geologicalReportService.generateGeologicalReport(request)
                .map(response -> {
                    log.info("地质报表生成成功: taskId={}, fileId={}", 
                            response.getTaskId(), response.getFileId());
                    return ResponseEntity.ok(response);
                })
                .onErrorResume(error -> {
                    log.error("地质报表生成失败", error);
                    GeologicalReportResponse errorResponse = GeologicalReportResponse.error(
                            "报表生成失败: " + error.getMessage());
                    return Mono.just(ResponseEntity.badRequest().body(errorResponse));
                });
    }
    
    /**
     * 仅处理剖面图片（不生成报表）
     */
    @PostMapping(value = "/process-section", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Mono<ResponseEntity<SectionProcessResponse>> processSection(
            @Valid @ModelAttribute GeologicalReportRequest request) {
        
        log.info("收到剖面处理请求: {}", request.getImageFile().getOriginalFilename());
        
        return geologicalReportService.processSectionImage(request)
                .map(ResponseEntity::ok)
                .onErrorResume(error -> {
                    log.error("剖面处理失败", error);
                    return Mono.just(ResponseEntity.badRequest().build());
                });
    }
    
    /**
     * 查询报表生成状态
     */
    @GetMapping("/status/{taskId}")
    public Mono<ResponseEntity<ReportStatusResponse>> getReportStatus(@PathVariable String taskId) {
        log.info("查询报表状态: taskId={}", taskId);
        
        return geologicalReportService.getReportStatus(taskId)
                .map(ResponseEntity::ok)
                .onErrorResume(error -> {
                    log.error("查询报表状态失败: taskId={}", taskId, error);
                    return Mono.just(ResponseEntity.badRequest().build());
                });
    }
    
    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("Geological Report Service is running");
    }
}
