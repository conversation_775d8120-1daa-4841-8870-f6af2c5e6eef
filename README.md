# 地质剖面报表生成服务（支持批量处理）

这是一个Java Spring Boot应用，用于调用Python接口批量处理地质剖面图片并生成动态Excel统计报表。

## 功能说明

1. **批量剖面处理**：调用Python接口 `/section-unified/process-batch` 批量处理多张地质剖面图片
2. **动态报表生成**：调用Python接口 `/api/reports/generate` 生成动态列数的Excel统计报表
3. **智能表格**：根据JSON数据中的最大钻孔数自动确定列数，根据剖面数确定行数
4. **完整流程**：批量上传图片 → 批量剖面识别 → 数据合并提取 → 动态生成Excel报表

## 项目结构

```
src/main/java/com/bluesheep/generate_report/
├── controller/
│   └── SimpleReportController.java     # 简化版REST控制器
├── service/
│   └── SimpleReportService.java        # 简化版业务服务
├── config/
│   └── HttpClientConfig.java           # HTTP客户端配置
├── dto/                                # 数据传输对象
│   ├── SectionProcessResponse.java
│   ├── ReportGenerateRequest.java
│   ├── ReportGenerateResponse.java
│   ├── ReportStatusResponse.java
│   ├── GeologicalReportRequest.java
│   └── GeologicalReportResponse.java
└── GenerateReportApplication.java      # 主启动类
```

## 配置说明

在 `application.properties` 中配置Python API地址：

```properties
# Python API Configuration
python.api.base-url=http://localhost:8000
python.api.timeout=300

# File Upload Configuration
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB
```

## API接口

### 简化版接口

#### 批量生成报表
- **URL**: `POST /api/simple-report/generate`
- **Content-Type**: `multipart/form-data`
- **参数**:
  - `files`: 多个地质剖面图片文件
- **响应**:
```json
{
  "success": true,
  "message": "报表生成成功",
  "report_url": "http://localhost:8000/static/report.xlsx",
  "file_count": 3,
  "filenames": "image1.jpg, image2.png, image3.jpg"
}
```

#### 单文件生成报表（兼容性）
- **URL**: `POST /api/simple-report/generate-single`
- **Content-Type**: `multipart/form-data`
- **参数**:
  - `file`: 单个地质剖面图片文件
- **响应**: 同上

#### 测试接口（调试用）

**剖面处理测试**:
- **URL**: `POST /api/simple-report/test-process`
- **Content-Type**: `multipart/form-data`
- **参数**:
  - `files`: 多个地质剖面图片文件
- **功能**: 仅进行剖面处理和数据转换测试，不生成报表
- **用途**: 调试数据结构和转换逻辑

**报表配置测试**:
- **URL**: `POST /api/simple-report/test-config`
- **Content-Type**: `multipart/form-data`
- **参数**:
  - `files`: 多个地质剖面图片文件
- **功能**: 生成完整的报表配置JSON，但不发送给Python接口
- **用途**: 调试报表配置格式和数据匹配性
- **响应**: 返回完整的报表配置JSON

#### 健康检查
- **URL**: `GET /api/simple-report/health`
- **响应**:
```json
{
  "status": "UP",
  "service": "Simple Report Service",
  "timestamp": 1640995200000
}
```

#### 接口说明
- **URL**: `GET /api/simple-report/info`
- **响应**: 返回接口使用说明

## 使用方法

### 1. 启动应用

```bash
# 编译项目
mvn clean compile

# 启动应用
mvn spring-boot:run
```

应用将在 `http://localhost:8080` 启动

### 2. 测试页面

访问 `http://localhost:8080/test.html` 可以使用Web界面测试报表生成功能。

### 3. 使用curl测试

```bash
# 批量处理多个文件
curl -X POST \
  http://localhost:8080/api/simple-report/generate \
  -H 'Content-Type: multipart/form-data' \
  -F 'files=@/path/to/image1.jpg' \
  -F 'files=@/path/to/image2.png' \
  -F 'files=@/path/to/image3.jpg'

# 单文件处理
curl -X POST \
  http://localhost:8080/api/simple-report/generate-single \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@/path/to/your/section-image.jpg'
```

### 4. 使用Postman测试

**批量处理**：
1. 创建POST请求到 `http://localhost:8080/api/simple-report/generate`
2. 选择Body → form-data
3. 添加多个key为`files`，类型为File的参数
4. 选择多个地质剖面图片文件
5. 发送请求

**单文件处理**：
1. 创建POST请求到 `http://localhost:8080/api/simple-report/generate-single`
2. 选择Body → form-data
3. 添加key为`file`，类型为File的参数
4. 选择地质剖面图片文件
5. 发送请求

## 处理流程

1. **批量图片上传**: 用户上传一个或多个地质剖面图片
2. **批量剖面处理**: 调用Python批量接口进行图像识别和数据提取
3. **动态列数分析**: 分析JSON数据获取最大钻孔数和剖面数
4. **动态数据转换**: 根据分析结果动态生成报表数据格式
5. **智能报表生成**: 调用Python接口生成动态列数的Excel报表
6. **状态轮询**: 等待报表生成完成
7. **返回结果**: 返回Excel文件下载链接

## 生成的Excel报表格式

报表包含动态列：
- 剖面号（固定）
- 孔号N + 孔跜N（N = 1 到 最大钻孔数）
- 备注（固定）

**例如**：如果检测到最大钻孔数为3，则生成列：
- 剖面号、孔号1、孔距1、孔号2、孔距2、孔号3、孔距3、备注

报表样式：
- 标题行：绿色背景，白色字体，居中对齐
- 表头行：浅绿色背景，加粗字体
- 数据行：带边框，居中对齐
- 冻结窗格：A3单元格

## 依赖的Python接口

### 1. 批量剖面处理接口
- **URL**: `POST /section-unified/process-batch`
- **功能**: 批量处理多张地质剖面图片，识别剖面、钻孔、间距等信息
- **返回**: 结构化的批量剖面数据（包含最大钻孔数统计）

### 2. 报表生成接口
- **URL**: `POST /api/reports/generate`
- **功能**: 根据配置生成Excel报表
- **返回**: 任务ID

### 3. 状态查询接口
- **URL**: `GET /api/reports/{task_id}/status`
- **功能**: 查询报表生成状态
- **返回**: 生成状态和结果文件链接

## 注意事项

1. 确保Python API服务正在运行
2. 单个图片文件大小不超过50MB，支持批量上传
3. 支持的图片格式：jpg、jpeg、png、gif、bmp
4. 批量处理可能需要更长时间，请耐心等待
5. 报表列数会根据实际数据动态调整
6. 生成的Excel文件链接有效期根据Python服务配置而定

## 错误处理

- 文件格式不支持：返回400错误
- Python API不可用：返回500错误
- 报表生成失败：返回详细错误信息
- 超时处理：最多等待10分钟

## 开发环境

- Java 17
- Spring Boot 3.5.3
- Maven 3.6+
- Python API服务（需要单独部署）
