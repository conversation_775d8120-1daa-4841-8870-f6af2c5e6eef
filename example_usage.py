import asyncio
import aiohttp
import json

async def test_report_service():
    """测试报表服务"""
    
    # 示例数据
    sales_data = [
        {"产品": "产品A", "销量": 100, "金额": 10000, "日期": "2024-01-01"},
        {"产品": "产品B", "销量": 150, "金额": 15000, "日期": "2024-01-02"},
        {"产品": "产品C", "销量": 200, "金额": 20000, "日期": "2024-01-03"},
    ]
    
    # 报表配置
    config = {
        "output_filename": "销售报表.xlsx",
        "sheets": [
            {
                "name": "销售数据",
                "data": sales_data,
                "merge_cells": [
                    {
                        "range": "A1:D1",
                        "value": "销售报表",
                        "style": {
                            "font_bold": True,
                            "font_size": 16,
                            "horizontal_align": "center",
                            "background_color": "#4CAF50",
                            "font_color": "#FFFFFF"
                        }
                    }
                ],
                "freeze_panes": "A3",
                "column_widths": {
                    "A": 15,
                    "B": 10,
                    "C": 12,
                    "D": 12
                },
                "styles": {
                    "A2:D2": {
                        "font_bold": True,
                        "background_color": "#E8F5E8",
                        "border": True
                    },
                    "A3:D5": {
                        "border": True,
                        "horizontal_align": "center"
                    }
                },
                "start_row": 2
            }
        ]
    }
    
    # 发送请求
    async with aiohttp.ClientSession() as session:
        # 生成报表
        async with session.post(
            "http://localhost:8000/api/reports/generate",
            json=config
        ) as response:
            result = await response.json()
            task_id = result["task_id"]
            print(f"任务ID: {task_id}")
        
        # 查询状态
        while True:
            async with session.get(
                f"http://localhost:8000/api/reports/{task_id}/status"
            ) as response:
                status = await response.json()
                print(f"状态: {status['status']}, 进度: {status['progress']}%")
                
                if status["status"] == "completed":
                    print(f"报表生成完成: {status['result_file']}")
                    break
                elif status["status"] == "failed":
                    print(f"报表生成失败: {status['error_message']}")
                    break
                
                await asyncio.sleep(1)

if __name__ == "__main__":
    asyncio.run(test_report_service())