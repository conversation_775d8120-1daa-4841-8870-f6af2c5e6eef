import PIL
from PIL import Image
from fastapi import File, UploadFile, HTTPException, Form, BackgroundTasks
from fastapi import APIRouter
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import os
import uuid
from datetime import datetime
from pathlib import Path
import cv2
import numpy as np
from loguru import logger

from ai_utils.obj_det.yolo_service import YoloModelType
from ai_utils.ocr_engine.engine import OcrModelType
from api.base import Config
from api.models import (
    DetectedSection, SectionDetectionResult, SectionProfile,
    SoilSampleInfo, SimpleDrillHole, SimpleSection, SimplifiedResult,
    UnifiedSectionResult
)
from api.section_detection_routes import SectionDetector
from api.section_detail_routes import SectionDetailRecognizer
from api.processor.section_integration_processor import SoilSample
from typing import Tuple
from api.section_integration_routes import detect_soil_samples_in_image_integrated
from utils.web import cleanup_temp_files, save_upload_file, validate_image_file


# 数据模型已移动到 api.models 模块中

# 统一处理路由器
unified_section_router = APIRouter(prefix="/section-unified", tags=["剖面统一处理API"])


def _assign_soil_samples_to_drill_hole(soil_detections: List[Dict], drill_hole_bbox: Dict[str, int]) -> List[Dict]:
    """
    根据钻孔位置分配土样检测结果
    使用X方向范围分配策略

    Args:
        soil_detections: 所有土样检测结果（原图坐标）
        drill_hole_bbox: 钻孔的边界框（原图坐标）

    Returns:
        属于该钻孔的土样检测结果列表
    """
    if not drill_hole_bbox or not soil_detections:
        logger.info(f"分配土样失败: soil_detections={len(soil_detections) if soil_detections else 0}, drill_hole_bbox={bool(drill_hole_bbox)}")
        return []

    assigned_detections = []

    # 获取钻孔的坐标信息
    drill_hole_x = drill_hole_bbox.get("x", 0)
    drill_hole_y = drill_hole_bbox.get("y", 0)
    drill_hole_width = drill_hole_bbox.get("width", 100)
    drill_hole_height = drill_hole_bbox.get("height", 100)
    drill_hole_x_center = drill_hole_x + drill_hole_width / 2
    drill_hole_y_center = drill_hole_y + drill_hole_height / 2

    logger.info(f"钻孔分配参数: bbox={drill_hole_bbox}")
    logger.info(f"钻孔中心坐标: ({drill_hole_x_center}, {drill_hole_y_center})")

    # 设置分配范围（钻孔中心左右各扩展50像素）
    assignment_range = 50
    x_min = drill_hole_x_center - assignment_range
    x_max = drill_hole_x_center + assignment_range

    logger.info(f"X方向分配范围: [{x_min}, {x_max}] (范围={assignment_range}像素)")

    # 记录所有土样的位置信息并进行分配
    for i, detection in enumerate(soil_detections):
        detection_bbox = detection.get("bbox", {})
        if detection_bbox:
            # 获取土样检测的坐标信息
            det_x = detection_bbox.get("x", 0)
            det_y = detection_bbox.get("y", 0)
            det_width = detection_bbox.get("width", 0)
            det_height = detection_bbox.get("height", 0)
            det_x_center = det_x + det_width / 2
            det_y_center = det_y + det_height / 2

            # 计算距离
            x_distance = abs(det_x_center - drill_hole_x_center)
            y_distance = abs(det_y_center - drill_hole_y_center)
            total_distance = ((det_x_center - drill_hole_x_center) ** 2 + (det_y_center - drill_hole_y_center) ** 2) ** 0.5

            logger.info(f"土样 {i+1}: 中心坐标({det_x_center}, {det_y_center}), X距离={x_distance:.1f}, Y距离={y_distance:.1f}, 总距离={total_distance:.1f}")

            # 判断土样是否在钻孔的分配范围内
            if x_min <= det_x_center <= x_max:
                assigned_detections.append(detection)
                logger.info(f"✅ 土样 {i+1} 分配成功: X方向范围内")
            else:
                logger.info(f"❌ 土样 {i+1} 超出X范围: 中心X={det_x_center}, 范围=[{x_min}, {x_max}]")

    logger.info(f"土样分配结果: 总共{len(soil_detections)}个土样，分配{len(assigned_detections)}个给当前钻孔")
    return assigned_detections


def _process_spacing_data(spacings: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    处理间距数据，保持OCR识别的原始文本格式

    Args:
        spacings: 原始间距数据列表

    Returns:
        处理后的简化间距数据列表，包含数值和原始文本格式
    """
    simplified_spacings = []

    for spacing in spacings:
        # 如果spacing有value字段，提取数值和原始文本
        if "value" in spacing:
            simplified_spacing = {
                "value": spacing.get("value"),  # 数值
                "original_text": spacing.get("original_text", str(spacing.get("value", ""))),  # 原始文本格式
                "confidence": spacing.get("confidence", 0.0),
                "bbox": spacing.get("bbox", {}),  # 边界框坐标
                "bbox_relative": spacing.get("bbox_relative", {})  # 相对坐标
            }
            simplified_spacings.append(simplified_spacing)
        # 如果有spacing_values数组，转换为多个spacing对象
        elif "spacing_values" in spacing:
            spacing_values = spacing.get("spacing_values", [])
            confidence = spacing.get("confidence", 0.0)
            for value in spacing_values:
                simplified_spacing = {
                    "value": value,  # 数值
                    "original_text": str(value),  # 如果没有原始文本，使用数值的字符串形式
                    "confidence": confidence,
                    "bbox": spacing.get("bbox", {}),  # 边界框坐标
                    "bbox_relative": spacing.get("bbox_relative", {})  # 相对坐标
                }
                simplified_spacings.append(simplified_spacing)

    return simplified_spacings


def _validate_soil_sample_data(sample: Dict[str, Any]) -> bool:
    """
    验证土样数据的完整性

    Args:
        sample: 土样数据字典

    Returns:
        是否通过验证
    """
    required_fields = ["sample_id", "sample_type", "depth", "elevation", "bbox"]

    for field in required_fields:
        if field not in sample:
            logger.warning(f"土样数据缺少必需字段: {field}")
            return False

        # 检查数值字段的有效性
        if field in ["depth", "elevation"]:
            value = sample[field]
            if not isinstance(value, (int, float)):
                logger.warning(f"土样数据字段 {field} 值无效: {value}")
                return False

        # 检查bbox字段的有效性
        if field == "bbox":
            bbox = sample[field]
            if not isinstance(bbox, dict) or not all(k in bbox for k in ["x", "y", "width", "height"]):
                logger.warning(f"土样数据bbox格式无效: {bbox}")
                return False

    return True


def create_simplified_result(unified_result: Dict[str, Any]) -> SimplifiedResult:
    """
    从完整的统一处理结果创建精简的结果对象

    Args:
        unified_result: 完整的统一处理结果

    Returns:
        精简的结果对象
    """
    sections = []

    logger.info("开始创建精简结果...")
    logger.info(f"统一结果结构: {list(unified_result.keys())}")

    # 从详细识别结果中提取剖面数据
    detail_results = unified_result.get("stage2_detail_recognition", {}).get("detail_results", [])
    logger.info(f"找到 {len(detail_results)} 个详细识别结果")

    for i, detail_result in enumerate(detail_results):
        logger.info(f"处理详细识别结果 {i+1}: success={detail_result.get('recognition_success', False)}")

        if detail_result.get("recognition_success", False):
            section_data_list = detail_result.get("sections", [])
            logger.info(f"详细识别结果 {i+1} 包含 {len(section_data_list)} 个剖面")

            for j, section_data in enumerate(section_data_list):
                logger.info(f"处理剖面 {j+1}: section_id={section_data.get('section_id', 'Unknown')}")
                drill_holes_data = section_data.get("drill_holes", [])
                logger.info(f"剖面 {j+1} 包含 {len(drill_holes_data)} 个钻孔")
                # 创建简化的钻孔列表
                drill_holes = []
                for k, drill_hole_data in enumerate(drill_holes_data):
                    hole_id = drill_hole_data.get("hole_id", "Unknown")
                    surface_elevation = drill_hole_data.get("surface_elevation", 0.0)
                    samples_data = drill_hole_data.get("samples", [])

                    logger.info(f"处理钻孔 {k+1}: hole_id={hole_id}, surface_elevation={surface_elevation}, samples={len(samples_data)}")

                    # 处理土样数据，使用新的SoilSampleInfo模型
                    simplified_samples = []
                    for l, sample in enumerate(samples_data):
                        logger.info(f"处理土样 {l+1}: {sample}")

                        # 如果sample是SoilSampleInfo对象，直接使用
                        if hasattr(sample, 'dict'):
                            simplified_samples.append(sample)
                        else:
                            # 如果是字典，转换为SoilSampleInfo对象
                            try:
                                soil_sample = SoilSampleInfo(
                                    sample_id=sample.get("sample_id", ""),
                                    depth=sample.get("depth", 0.0),
                                    elevation=sample.get("elevation", 0.0),
                                    sample_type=sample.get("sample_type", "未知类型"),
                                    bbox=sample.get("bbox"),
                                    bbox_relative=sample.get("bbox_relative"),
                                    confidence=sample.get("confidence")
                                )
                                simplified_samples.append(soil_sample)
                                logger.info(f"成功转换土样: {soil_sample.sample_id}")
                            except Exception as e:
                                logger.warning(f"转换土样数据时发生错误: {str(e)}, 数据: {sample}")

                    drill_hole = SimpleDrillHole(
                        hole_id=hole_id,
                        surface_elevation=surface_elevation,
                        bbox=drill_hole_data.get("bbox", {}),
                        bbox_relative=drill_hole_data.get("bbox_relative", {}),
                        x_coordinate=drill_hole_data.get("x_coordinate"),
                        assigned_spacing=drill_hole_data.get("assigned_spacing"),
                        is_terminal_hole=drill_hole_data.get("is_terminal_hole", False),
                        samples=simplified_samples
                    )
                    drill_holes.append(drill_hole)
                    logger.info(f"成功创建钻孔: {hole_id}, 包含 {len(simplified_samples)} 个土样")

                # 简化间距数据处理
                simplified_spacings = _process_spacing_data(section_data.get("spacings", []))

                section = SimpleSection(
                    section_id=section_data.get("section_id", "Unknown"),
                    bbox=section_data.get("bbox", {}),  # 从SectionProfile的bbox字段获取
                    bbox_relative=section_data.get("bbox_relative", {}),  # 从SectionProfile的bbox_relative字段获取
                    drill_holes=drill_holes,
                    spacings=simplified_spacings
                )
                sections.append(section)

    # 创建统计摘要
    total_soil_samples = sum(
        len(drill_hole.samples)
        for section in sections
        for drill_hole in section.drill_holes
    )

    summary = {
        "total_sections": len(sections),
        "total_drill_holes": sum(len(section.drill_holes) for section in sections),
        "total_spacings": sum(len(section.spacings) for section in sections),
        "total_soil_samples": total_soil_samples
    }

    # 记录处理结果统计
    logger.info(f"三阶段处理完成统计: {summary['total_sections']}个剖面, "
               f"{summary['total_drill_holes']}个钻孔, "
               f"{summary['total_spacings']}个间距, "
               f"{summary['total_soil_samples']}个土样")

    # 验证每个土样是否包含必需的字段
    valid_samples = 0
    for section in sections:
        for drill_hole in section.drill_holes:
            for sample in drill_hole.samples:
                if all(field in sample for field in ["bbox", "depth", "elevation", "sample_type"]):
                    valid_samples += 1

    logger.info(f"数据完整性验证: {valid_samples}/{total_soil_samples} 个土样包含完整的必需字段")

    # 创建精简结果
    simplified_result = SimplifiedResult(
        success=unified_result.get("success", False),
        file_id=unified_result.get("file_id", ""),
        filename=unified_result.get("filename", ""),
        image_width=unified_result.get("image_width", 0),
        image_height=unified_result.get("image_height", 0),
        sections=sections,
        summary=summary,
        timestamp=unified_result.get("timestamp", ""),
        json_file_url=unified_result.get("json_file_url")
    )

    return simplified_result


# 统一处理结果数据模型已移动到 api.models 模块中


def process_unified_section(
        image_input,
        file_id: str,
        filename: str,
        # 第一阶段参数：区域检测
        detection_confidence: float = Config.DEFAULT_CONFIDENCE,
        detection_iou: float = Config.DEFAULT_IOU,
        # 第二阶段参数：详细识别
        detail_confidence: float = Config.DEFAULT_CONFIDENCE,
        detail_iou: float = Config.DEFAULT_IOU,
        detail_model_type: str = YoloModelType.SECTION_DETAIL_RECOGNITION,
        ocr_model: str = 'paddle',  # 新增OCR模型参数
        # 第三阶段参数：土样检测
        enable_soil_sample_detection: bool = True,
        soil_detection_confidence: float = Config.DEFAULT_CONFIDENCE,
        soil_detection_iou: float = Config.DEFAULT_IOU,
        enable_slice: bool = False,
        slice_width: int = 640,
        slice_height: int = 640,
        overlap_ratio: float = 0.2,
        nms_threshold: float = 0.5,
        # 输出参数
        return_image: bool = True,
        skip_detail_recognition: bool = False,
        skip_soil_sample_detection: bool = False,
        save_json: bool = True
) -> Dict[str, Any]:
    """
    统一剖面处理方法 - 串联执行区域检测、详细识别和土样检测

    Args:
        image_input: 输入图像（numpy数组或图片路径）
        file_id: 文件ID
        filename: 文件名
        detection_confidence: 剖面区域检测置信度阈值
        detection_iou: 剖面区域检测IoU阈值
        detail_confidence: 详细识别置信度阈值
        detail_iou: 详细识别IoU阈值
        detail_model_type: 详细识别模型类型
        ocr_model: OCR模型类型 ('paddle', 'cnocr', 'rapid')
        enable_soil_sample_detection: 是否启用土样检测
        soil_detection_confidence: 土样检测置信度阈值
        soil_detection_iou: 土样检测IoU阈值
        enable_slice: 是否启用分割检测
        slice_width: 分割图片宽度
        slice_height: 分割图片高度
        overlap_ratio: 切片重叠比例
        nms_threshold: NMS阈值
        return_image: 是否返回标注结果图片
        skip_detail_recognition: 是否跳过详细识别步骤
        skip_soil_sample_detection: 是否跳过土样检测步骤
        save_json: 是否保存JSON结果文件

    Returns:
        包含三个阶段完整结果的字典
    """
    
    temp_files = []
    
    # 创建必要的目录
    crop_dir = os.path.join(Config.TEMP_DIR, f"{file_id}_crops")
    os.makedirs(crop_dir, exist_ok=True)
    temp_files.append(crop_dir)
    
    # 读取图片
    if isinstance(image_input, str):
        test_img = cv2.imread(image_input)
        if test_img is None:
            raise ValueError("无法读取图片文件，请检查文件格式")
    elif isinstance(image_input, Image.Image):
        test_img = np.array(image_input)
    else:
        test_img = image_input.copy()

    # 获取原始图片尺寸
    img_height, img_width = test_img.shape[:2]
    logger.info(f"原始图片尺寸: {img_width}x{img_height}")

    # ==================== 第一阶段：剖面区域检测 ====================
    logger.info(f"开始第一阶段：剖面区域检测 - {filename}")

    # 创建剖面检测器实例
    detector = SectionDetector(
        confidence_threshold=detection_confidence,
        iou_threshold=detection_iou
    )

    # 执行剖面区域检测
    detected_sections = detector.detect_sections(test_img)

    # 裁剪检测到的剖面区域
    if detected_sections:
        detected_sections = detector.crop_sections(test_img, detected_sections, crop_dir)

    # 生成检测结果图片
    detection_image_url = None
    if return_image:
        output_path = os.path.join(Config.OUTPUT_DIR, f"{file_id}_detection_output.jpg")
        detector.save_detection_result(test_img, detected_sections, output_path)
        temp_files.append(output_path)
        detection_image_url = f"/static/{file_id}_detection_output.jpg"

    # 构建第一阶段结果
    detection_result = {
        "success": True,
        "file_id": file_id,
        "filename": filename,
        "detected_sections": [section.dict() for section in detected_sections],
        "total_sections": len(detected_sections),
        "parameters": {
            "confidence_threshold": detection_confidence,
            "iou_threshold": detection_iou
        },
        "timestamp": datetime.now().isoformat(),
        "result_image_url": detection_image_url
    }

    logger.info(f"第一阶段完成：检测到 {len(detected_sections)} 个剖面区域")

    # ==================== 第二阶段：详细识别 ====================
    detail_results = []
    total_drill_holes = 0
    total_spacings = 0
    successful_recognitions = 0

    if not skip_detail_recognition and detected_sections:
        logger.info(f"开始第二阶段：对 {len(detected_sections)} 个区域进行详细识别")

        for i, section in enumerate(detected_sections):
            if section.cropped_image_path and os.path.exists(section.cropped_image_path):
                try:
                    # 获取裁剪偏移量
                    crop_bbox = section.bbox
                    crop_offset = (crop_bbox["x"], crop_bbox["y"])

                    # 创建详细识别器，传递裁剪偏移量
                    recognizer = SectionDetailRecognizer(
                        model_type=detail_model_type,
                        confidence_threshold=detail_confidence,
                        iou_threshold=detail_iou,
                        ocr_model_name=ocr_model,  # 添加OCR模型参数
                        original_image_width=img_width,  # 传入原始图片宽度
                        original_image_height=img_height,  # 传入原始图片高度
                        crop_offset=crop_offset  # 传递裁剪偏移量，确保钻孔坐标转换为原图坐标
                    )

                    logger.info(f"区域 {section.section_index} 详细识别: 裁剪偏移量={crop_offset}")

                    # 对裁剪的图片进行详细识别
                    sections = recognizer.recognize(section.cropped_image_path)

                    # 获取识别摘要
                    summary = recognizer.get_recognition_summary()

                    # 生成详细识别结果图片
                    detail_image_url = None
                    if return_image:
                        detail_output_path = os.path.join(Config.OUTPUT_DIR, f"{file_id}_detail_{section.section_index}_output.jpg")

                        try:
                            # 使用已创建的识别器生成带标注的图片
                            result_image = None

                            logger.info(f"开始为区域 {section.section_index} 生成标注图片")

                            # 使用已有的检测器生成带标注的图片
                            if recognizer.detector and hasattr(recognizer.detector, 'predict'):
                                try:
                                    # 使用已有检测器的result_image属性（如果存在）
                                    if hasattr(recognizer, 'result_image') and recognizer.result_image is not None:
                                        result_image = recognizer.result_image
                                        logger.info(f"使用已有的标注图片 - 区域 {section.section_index}")
                                    else:
                                        # 重新调用predict生成带标注的图片
                                        result_image = recognizer.detector.predict(export_json=False)
                                        logger.info(f"重新生成带标注图片 - 区域 {section.section_index}")

                                    if result_image is not None:
                                        logger.info(f"成功获取带标注图片 - 区域 {section.section_index}, 图片尺寸: {result_image.shape}")
                                        logger.info(f"检测到 {len(recognizer.detector.detection_results)} 个目标")
                                    else:
                                        logger.warning(f"检测器返回None - 区域 {section.section_index}")
                                except Exception as e:
                                    logger.warning(f"检测器生成标注图片失败 - 区域 {section.section_index}: {str(e)}")
                            else:
                                logger.warning(f"检测器不可用 - 区域 {section.section_index}")

                            # 如果检测器生成失败，使用裁剪图片作为备选
                            if result_image is None and section.cropped_image_path and os.path.exists(section.cropped_image_path):
                                result_image = cv2.imread(section.cropped_image_path)
                                logger.info(f"使用裁剪图片作为备选 - 区域 {section.section_index}")

                            # 保存图片
                            if result_image is not None:
                                success = cv2.imwrite(detail_output_path, result_image)
                                if success:
                                    temp_files.append(detail_output_path)
                                    detail_image_url = f"/static/{file_id}_detail_{section.section_index}_output.jpg"
                                    logger.info(f"详细识别结果图片已保存: {detail_output_path}")
                                else:
                                    logger.error(f"保存详细识别结果图片失败: {detail_output_path}")
                            else:
                                logger.error(f"无法获取结果图片 - 区域 {section.section_index}")

                        except Exception as e:
                            logger.error(f"生成详细识别图片时发生错误: {str(e)}")
                            import traceback
                            logger.error(f"详细错误: {traceback.format_exc()}")

                    # 构建该区域的详细识别结果
                    section_detail_result = {
                        "section_index": section.section_index,
                        "cropped_image_path": section.cropped_image_path,
                        "recognition_success": True,
                        "sections": [section.dict() for section in sections],
                        "summary": summary,
                        "parameters": {
                            "model_type": detail_model_type,
                            "confidence_threshold": detail_confidence,
                            "iou_threshold": detail_iou
                        },
                        "result_image_url": detail_image_url
                    }

                    detail_results.append(section_detail_result)
                    successful_recognitions += 1

                    # 累计统计
                    total_drill_holes += summary.get('total_drill_holes', 0)
                    total_spacings += summary.get('total_spacings', 0)

                    logger.info(f"区域 {i+1} 识别完成：{summary.get('total_sections', 0)} 个剖面")

                except Exception as e:
                    logger.error(f"区域 {i+1} 详细识别失败: {str(e)}")
                    detail_results.append({
                        "section_index": section.section_index,
                        "cropped_image_path": section.cropped_image_path,
                        "recognition_success": False,
                        "error": str(e),
                        "sections": [],
                        "summary": {},
                        "result_image_url": None
                    })
            else:
                logger.warning(f"区域 {i+1} 裁剪图片不存在，跳过详细识别")
                detail_results.append({
                    "section_index": section.section_index,
                    "cropped_image_path": section.cropped_image_path,
                    "recognition_success": False,
                    "error": "裁剪图片不存在",
                    "sections": [],
                    "summary": {},
                    "result_image_url": None
                })

    logger.info(f"第二阶段完成：成功识别 {successful_recognitions} 个区域")

    # ==================== 第三阶段：土样检测 ====================
    soil_sample_results = []
    total_soil_detections = 0
    successful_soil_detection = 0

    if enable_soil_sample_detection and not skip_soil_sample_detection and detail_results:
        logger.info(f"开始第三阶段：对 {len(detail_results)} 个识别结果进行土样检测")

        for detail_result in detail_results:
            if detail_result.get("recognition_success", False):
                try:
                    section_index = detail_result.get("section_index", "Unknown")
                    cropped_image_path = detail_result.get("cropped_image_path")

                    if cropped_image_path and os.path.exists(cropped_image_path):
                        logger.info(f"开始检测区域 {section_index} 的土样")

                        # 执行土样检测 - 使用与process_soil_sample_detections相同的检测函数
                        from api.processor.section_integration_processor import detect_soil_samples_in_image

                        soil_detections = detect_soil_samples_in_image(
                            image_input=cropped_image_path,
                            confidence_threshold=soil_detection_confidence,
                            iou_threshold=soil_detection_iou,
                            enable_slice=enable_slice,
                            slice_width=slice_width,
                            slice_height=slice_height,
                            overlap_ratio=overlap_ratio,
                            nms_threshold=nms_threshold
                        )

                        # 获取对应的剖面区域信息，用于坐标转换
                        corresponding_section = None
                        for section in detected_sections:
                            if section.section_index == section_index:
                                corresponding_section = section
                                break

                        # 将土样检测结果的坐标转换为原图坐标
                        if corresponding_section and soil_detections:
                            crop_bbox = corresponding_section.bbox
                            x_offset, y_offset = crop_bbox["x"], crop_bbox["y"]

                            logger.info(f"土样坐标转换: 裁剪区域={crop_bbox}, 偏移量=({x_offset}, {y_offset})")

                            for i, detection in enumerate(soil_detections):
                                bbox = detection.get("bbox", {})
                                if bbox:
                                    # 记录转换前的坐标
                                    original_bbox = bbox.copy()

                                    # 转换为原图坐标
                                    bbox["x"] += x_offset
                                    bbox["y"] += y_offset

                                    # 重新计算相对坐标（基于原图）
                                    detection["bbox_relative"] = {
                                        "x": float(bbox["x"]) / img_width,
                                        "y": float(bbox["y"]) / img_height,
                                        "width": float(bbox["width"]) / img_width,
                                        "height": float(bbox["height"]) / img_height
                                    }

                                    if i < 3:  # 只记录前3个土样的转换详情
                                        logger.info(f"土样 {i+1} 坐标转换: {original_bbox} -> {bbox}")

                            logger.info(f"已将 {len(soil_detections)} 个土样检测结果转换为原图坐标")

                        # 生成土样检测结果图片
                        soil_detection_image_url = None
                        if return_image and soil_detections:
                            try:
                                # 读取裁剪图片
                                crop_image = cv2.imread(cropped_image_path)
                                if crop_image is not None:
                                    # 在图片上绘制检测结果
                                    result_image = crop_image.copy()

                                    # 获取裁剪偏移量，用于坐标转换
                                    crop_bbox = corresponding_section.bbox
                                    x_offset, y_offset = crop_bbox["x"], crop_bbox["y"]

                                    logger.info(f"生成土样检测图片: 使用裁剪图片，需要将原图坐标转换回裁剪坐标")

                                    for i, detection in enumerate(soil_detections):
                                        bbox = detection.get("bbox", {})
                                        if bbox:
                                            # 将原图坐标转换回裁剪图片坐标
                                            crop_x = bbox["x"] - x_offset
                                            crop_y = bbox["y"] - y_offset
                                            w, h = bbox["width"], bbox["height"]
                                            confidence = detection.get("confidence", 0.0)

                                            # 确保坐标在裁剪图片范围内
                                            crop_height, crop_width = crop_image.shape[:2]
                                            if (crop_x >= 0 and crop_y >= 0 and
                                                crop_x + w <= crop_width and crop_y + h <= crop_height):

                                                # 绘制检测框（红色）
                                                cv2.rectangle(result_image, (crop_x, crop_y), (crop_x + w, crop_y + h), (0, 0, 255), 2)

                                                # 绘制置信度标签
                                                label = f"土样: {confidence:.2f}"
                                                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
                                                cv2.rectangle(result_image, (crop_x, crop_y - 20), (crop_x + label_size[0], crop_y), (0, 0, 255), -1)
                                                cv2.putText(result_image, label, (crop_x, crop_y - 5),
                                                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

                                                if i < 3:  # 记录前3个土样的绘制详情
                                                    logger.info(f"土样 {i+1} 绘制: 原图坐标({bbox['x']}, {bbox['y']}) -> 裁剪坐标({crop_x}, {crop_y})")
                                            else:
                                                logger.warning(f"土样 {i+1} 超出裁剪图片范围: 裁剪坐标({crop_x}, {crop_y}), 图片尺寸({crop_width}, {crop_height})")

                                    # 保存检测结果图片
                                    soil_output_path = os.path.join(Config.OUTPUT_DIR, f"{file_id}_soil_detection_{section_index}_output.jpg")
                                    success = cv2.imwrite(soil_output_path, result_image)
                                    if success:
                                        temp_files.append(soil_output_path)
                                        soil_detection_image_url = f"/static/{file_id}_soil_detection_{section_index}_output.jpg"
                                        logger.info(f"土样检测结果图片已保存: {soil_output_path}")

                            except Exception as e:
                                logger.error(f"生成土样检测图片时发生错误: {str(e)}")

                        # 使用整合后的剖面处理器进行土样处理
                        from api.processor.section_integration_processor import SectionIntegrationProcessor
                        soil_processor = SectionIntegrationProcessor()

                        # 获取该区域的剖面数据，为每个钻孔分配土样
                        sections = detail_result.get("sections", [])
                        total_assigned_samples = 0
                        unassigned_soil_detections = soil_detections.copy()  # 跟踪未分配的土样

                        for section_data in sections:
                            drill_holes = section_data.get("drill_holes", [])
                            # 获取该剖面的间距信息，用于精确计算深度
                            section_spacings = section_data.get("spacings", [])

                            # 实现钻孔间距分配逻辑：按x轴坐标排序，最后一个钻孔作为终孔
                            drill_holes_with_spacing = _assign_spacing_to_drill_holes(drill_holes, section_spacings)

                            # 使用新的土样处理方法
                            processed_drill_holes, unassigned_soil_detections = _process_soil_samples_with_integration(
                                soil_detections, drill_holes_with_spacing, soil_processor
                            )

                            # 更新统计信息
                            for drill_hole in processed_drill_holes:
                                total_assigned_samples += len(drill_hole.samples)

                            logger.info(f"剖面处理完成: {len(processed_drill_holes)} 个钻孔, {total_assigned_samples} 个土样")

                            # 将处理后的钻孔添加到剖面数据中
                            section_data["drill_holes"] = [drill_hole.dict() for drill_hole in processed_drill_holes]

                        # 构建该区域的土样检测结果
                        section_soil_result = {
                            "section_index": section_index,
                            "cropped_image_path": cropped_image_path,
                            "detection_success": True,
                            "soil_detections": soil_detections,
                            "total_detections": len(soil_detections),
                            "detection_image_url": soil_detection_image_url,
                            "parameters": {
                                "confidence": soil_detection_confidence,
                                "iou_threshold": soil_detection_iou,
                                "enable_slice": enable_slice,
                                "slice_width": slice_width,
                                "slice_height": slice_height,
                                "overlap_ratio": overlap_ratio,
                                "nms_threshold": nms_threshold
                            }
                        }

                        soil_sample_results.append(section_soil_result)
                        total_soil_detections += len(soil_detections)
                        successful_soil_detection += 1

                        logger.info(f"区域 {section_index} 土样检测完成，检测到 {len(soil_detections)} 个土样")

                    else:
                        logger.warning(f"区域 {section_index} 裁剪图片不存在，跳过土样检测")
                        soil_sample_results.append({
                            "section_index": section_index,
                            "cropped_image_path": cropped_image_path,
                            "detection_success": False,
                            "error": "裁剪图片不存在",
                            "soil_detections": [],
                            "total_detections": 0,
                            "detection_image_url": None
                        })

                except Exception as e:
                    logger.error(f"区域 {detail_result.get('section_index', 'Unknown')} 土样检测失败: {str(e)}")
                    soil_sample_results.append({
                        "section_index": detail_result.get("section_index", "Unknown"),
                        "detection_success": False,
                        "error": str(e),
                        "soil_detections": [],
                        "total_detections": 0,
                        "detection_image_url": None
                    })

        logger.info(f"第三阶段完成：成功检测 {successful_soil_detection} 个区域的土样，共检测到 {total_soil_detections} 个土样")

    # 构建统一结果
    unified_result = {
        "success": True,
        "file_id": file_id,
        "filename": filename,
        "image_width": img_width,
        "image_height": img_height,
        "stage1_detection": detection_result,
        "stage2_detail_recognition": {
            "success": successful_recognitions > 0 if not skip_detail_recognition else True,
            "total_processed_sections": len(detail_results),
            "successful_recognitions": successful_recognitions,
            "total_drill_holes": total_drill_holes,
            "total_spacings": total_spacings,
            "detail_results": detail_results,
            "parameters": {
                "model_type": detail_model_type,
                "confidence_threshold": detail_confidence,
                "iou_threshold": detail_iou,
                "skip_detail_recognition": skip_detail_recognition
            }
        },
        "stage3_soil_sample_detection": {
            "success": successful_soil_detection > 0 if enable_soil_sample_detection and not skip_soil_sample_detection else True,
            "total_processed_sections": len([r for r in soil_sample_results if r.get("detection_success", False)]),
            "successful_detections": successful_soil_detection,
            "total_soil_detections": total_soil_detections,
            "soil_detection_results": soil_sample_results,
            "parameters": {
                "enable_soil_sample_detection": enable_soil_sample_detection,
                "soil_detection_confidence": soil_detection_confidence,
                "soil_detection_iou": soil_detection_iou,
                "enable_slice": enable_slice,
                "slice_width": slice_width,
                "slice_height": slice_height,
                "overlap_ratio": overlap_ratio,
                "nms_threshold": nms_threshold,
                "skip_soil_sample_detection": skip_soil_sample_detection
            }
        },
        "summary": {
            "total_detected_sections": len(detected_sections),
            "total_processed_sections": len(detail_results),
            "successful_recognitions": successful_recognitions,
            "total_drill_holes": total_drill_holes,
            "total_spacings": total_spacings,
            "total_soil_detections": total_soil_detections
        },
        "parameters": {
            "detection_confidence": detection_confidence,
            "detection_iou": detection_iou,
            "detail_confidence": detail_confidence,
            "detail_iou": detail_iou,
            "detail_model_type": detail_model_type,
            "enable_soil_sample_processing": enable_soil_sample_detection,
            "return_image": return_image,
            "skip_detail_recognition": skip_detail_recognition,
            "skip_soil_sample_detection": skip_soil_sample_detection,
            "save_json": save_json
        },
        "timestamp": datetime.now().isoformat(),
        "temp_files": temp_files
    }

    # 保存JSON结果文件
    json_file_url = None
    simplified_json_url = None

    if save_json:
        try:
            import json

            # 保存完整结果
            json_output_path = os.path.join(Config.OUTPUT_DIR, f"{file_id}_unified_results.json")
            json_data = unified_result.copy()
            json_data.pop("temp_files", None)

            with open(json_output_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2, default=str)

            temp_files.append(json_output_path)
            json_file_url = f"/static/{file_id}_unified_results.json"
            unified_result["json_file_url"] = json_file_url

            # 创建并保存精简结果
            simplified_result = create_simplified_result(unified_result)
            simplified_output_path = os.path.join(Config.OUTPUT_DIR, f"{file_id}_simplified_results.json")

            with open(simplified_output_path, 'w', encoding='utf-8') as f:
                json.dump(simplified_result.dict(), f, ensure_ascii=False, indent=2, default=str)

            temp_files.append(simplified_output_path)
            simplified_json_url = f"/static/{file_id}_simplified_results.json"
            unified_result["simplified_json_url"] = simplified_json_url

            logger.info(f"完整结果JSON文件已保存: {json_output_path}")
            logger.info(f"精简结果JSON文件已保存: {simplified_output_path}")

            # 将精简结果添加到返回数据中
            unified_result["simplified_result"] = simplified_result.dict()

        except Exception as e:
            logger.error(f"保存JSON结果文件失败: {str(e)}")

    return unified_result


@unified_section_router.post("/process-full", response_model=UnifiedSectionResult)
async def unified_section_process_full(
        background_tasks: BackgroundTasks,
        file: UploadFile = File(...),
        # 第一阶段参数：区域检测
        detection_confidence: float = Form(default=Config.DEFAULT_CONFIDENCE, description="剖面检测置信度阈值"),
        detection_iou: float = Form(default=Config.DEFAULT_IOU, description="剖面检测IoU阈值"),
        # 第二阶段参数：详细识别
        detail_confidence: float = Form(default=Config.DEFAULT_CONFIDENCE, description="详细识别置信度阈值"),
        detail_iou: float = Form(default=Config.DEFAULT_IOU, description="详细识别IoU阈值"),
        detail_model_type: str = Form(default=YoloModelType.SECTION_DETAIL_RECOGNITION, description="详细识别模型类型"),
        ocr_model: str = Form(default='paddle', description="OCR模型类型"),
        # 第三阶段参数：土样检测
        enable_soil_sample_detection: bool = Form(default=True, description="是否启用土样检测"),
        soil_detection_confidence: float = Form(default=Config.DEFAULT_CONFIDENCE, description="土样检测置信度阈值"),
        soil_detection_iou: float = Form(default=Config.DEFAULT_IOU, description="土样检测IoU阈值"),
        enable_slice: bool = Form(default=False, description="是否启用分割检测"),
        slice_width: int = Form(default=640, description="分割图片宽度"),
        slice_height: int = Form(default=640, description="分割图片高度"),
        overlap_ratio: float = Form(default=0.2, description="切片重叠比例"),
        nms_threshold: float = Form(default=0.5, description="NMS阈值"),
        # 输出参数
        return_image: bool = Form(default=True, description="是否返回标注结果图片"),
        skip_detail_recognition: bool = Form(default=False, description="是否跳过详细识别（仅进行区域检测）"),
        skip_soil_sample_detection: bool = Form(default=False, description="是否跳过土样检测"),
        save_json: bool = Form(default=True, description="是否保存JSON结果文件")
):
    """
    统一剖面处理接口 - 串联执行区域检测、详细识别和土样检测

    处理流程：
    1. 第一阶段：调用 section_detection 检测剖面区域并裁剪
    2. 第二阶段：对每个裁剪的区域调用 section_detail 进行详细识别
    3. 第三阶段：对每个裁剪的区域调用 soil_sample detect 进行土样检测

    Args:
        file: 上传的图片文件
        detection_confidence: 剖面区域检测置信度阈值
        detection_iou: 剖面区域检测IoU阈值
        detail_confidence: 详细识别置信度阈值
        detail_iou: 详细识别IoU阈值
        detail_model_type: 详细识别模型类型
        ocr_model: OCR模型类型
        enable_soil_sample_detection: 是否启用土样检测
        soil_detection_confidence: 土样检测置信度阈值
        soil_detection_iou: 土样检测IoU阈值
        enable_slice: 是否启用分割检测
        slice_width: 分割图片宽度
        slice_height: 分割图片高度
        overlap_ratio: 切片重叠比例
        nms_threshold: NMS阈值
        return_image: 是否返回标注结果图片
        skip_detail_recognition: 是否跳过详细识别步骤
        skip_soil_sample_detection: 是否跳过土样检测步骤
        save_json: 是否保存JSON结果文件

    Returns:
        包含三个阶段完整结果的统一响应，包括JSON文件URL
    """

    # 验证文件
    if not validate_image_file(file, Config.ALLOWED_EXTENSIONS):
        raise HTTPException(status_code=400, detail="不支持的文件格式")

    file_id = str(uuid.uuid4())

    # 创建必要的目录
    input_path = os.path.join(Config.UPLOAD_DIR, f"{file_id}_input{Path(file.filename).suffix}")

    try:
        # 保存上传的文件
        await save_upload_file(file, input_path, Config.MAX_FILE_SIZE)

        # 调用统一流程处理方法
        result = process_unified_section(
            image_input=input_path,
            file_id=file_id,
            filename=file.filename,
            detection_confidence=detection_confidence,
            detection_iou=detection_iou,
            detail_confidence=detail_confidence,
            detail_iou=detail_iou,
            detail_model_type=detail_model_type,
            ocr_model=ocr_model,  # 传递OCR模型参数
            enable_soil_sample_detection=enable_soil_sample_detection,
            soil_detection_confidence=soil_detection_confidence,
            soil_detection_iou=soil_detection_iou,
            enable_slice=enable_slice,
            slice_width=slice_width,
            slice_height=slice_height,
            overlap_ratio=overlap_ratio,
            nms_threshold=nms_threshold,
            return_image=return_image,
            skip_detail_recognition=skip_detail_recognition,
            skip_soil_sample_detection=skip_soil_sample_detection,
            save_json=save_json
        )

        # 构建API响应格式
        detection_result = SectionDetectionResult(
            success=result["stage1_detection"]["success"],
            file_id=result["file_id"],
            filename=result["filename"],
            detected_sections=[DetectedSection(**section) for section in result["stage1_detection"]["detected_sections"]],
            total_sections=result["stage1_detection"]["total_sections"],
            parameters=result["stage1_detection"]["parameters"],
            timestamp=result["stage1_detection"]["timestamp"],
            result_image_url=result["stage1_detection"]["result_image_url"]
        )

        # 构建统一响应
        unified_result = UnifiedSectionResult(
            success=result["success"],
            file_id=result["file_id"],
            filename=result["filename"],
            image_width=result["image_width"],
            image_height=result["image_height"],
            detection_result=detection_result,
            detail_results=result["stage2_detail_recognition"]["detail_results"],
            soil_sample_results=result["stage3_soil_sample_detection"]["soil_detection_results"],
            total_detected_sections=result["summary"]["total_detected_sections"],
            total_recognized_sections=result["summary"]["successful_recognitions"],
            total_drill_holes=result["summary"]["total_drill_holes"],
            total_spacings=result["summary"]["total_spacings"],
            total_soil_samples=result["summary"]["total_soil_detections"],
            parameters=result["parameters"],
            timestamp=result["timestamp"],
            json_file_url=result.get("json_file_url"),
            simplified_json_url=result.get("simplified_json_url"),
            simplified_result=result.get("simplified_result")
        )

        logger.info(f"统一处理完成: {file.filename}")
        logger.info(f"检测阶段: {result['summary']['total_detected_sections']} 个区域")
        logger.info(f"识别阶段: {result['summary']['successful_recognitions']} 个成功, {result['summary']['total_drill_holes']} 个钻孔, {result['summary']['total_spacings']} 个间距")
        logger.info(f"土样检测阶段: {result['summary']['total_soil_detections']} 个土样检测")
        if result.get("json_file_url"):
            logger.info(f"完整JSON结果文件: {result['json_file_url']}")
        if result.get("simplified_json_url"):
            logger.info(f"精简JSON结果文件: {result['simplified_json_url']}")
        if result.get("simplified_result"):
            simplified = result["simplified_result"]
            logger.info(f"精简结果: {simplified['summary']['total_sections']}个剖面, {simplified['summary']['total_drill_holes']}个钻孔, {simplified['summary']['total_soil_samples']}个土样")

        # 添加后台任务清理临时文件
        def cleanup_after_delay():
            import time
            time.sleep(300)  # 5分钟后清理
            cleanup_temp_files([input_path] + result.get("temp_files", []))

        background_tasks.add_task(cleanup_after_delay)

        return unified_result

    except HTTPException:
        cleanup_temp_files([input_path])
        raise
    except Exception as e:
        cleanup_temp_files([input_path])
        logger.error(f"统一处理过程中发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")


@unified_section_router.post("/process", response_model=SimplifiedResult)
async def unified_section_process(
        background_tasks: BackgroundTasks,
        file: UploadFile = File(...),
        # 第一阶段参数：区域检测
        detection_confidence: float = Form(default=Config.DEFAULT_CONFIDENCE, description="剖面检测置信度阈值"),
        detection_iou: float = Form(default=Config.DEFAULT_IOU, description="剖面检测IoU阈值"),
        # 第二阶段参数：详细识别
        detail_confidence: float = Form(default=Config.DEFAULT_CONFIDENCE, description="详细识别置信度阈值"),
        detail_iou: float = Form(default=Config.DEFAULT_IOU, description="详细识别IoU阈值"),
        detail_model_type: str = Form(default=YoloModelType.SECTION_DETAIL_RECOGNITION, description="详细识别模型类型"),
        ocr_model: str = Form(default=OcrModelType.RAPID, description="OCR模型类型"),
        # 第三阶段参数：土样检测
        enable_soil_sample_detection: bool = Form(default=True, description="是否启用土样检测"),
        soil_detection_confidence: float = Form(default=Config.DEFAULT_CONFIDENCE, description="土样检测置信度阈值"),
        soil_detection_iou: float = Form(default=Config.DEFAULT_IOU, description="土样检测IoU阈值"),
        enable_slice: bool = Form(default=False, description="是否启用分割检测"),
        slice_width: int = Form(default=640, description="分割图片宽度"),
        slice_height: int = Form(default=640, description="分割图片高度"),
        overlap_ratio: float = Form(default=0.2, description="切片重叠比例"),
        nms_threshold: float = Form(default=0.5, description="NMS阈值"),
        # 输出参数
        return_image: bool = Form(default=False, description="是否返回标注结果图片"),
        skip_detail_recognition: bool = Form(default=False, description="是否跳过详细识别（仅进行区域检测）"),
        skip_soil_sample_detection: bool = Form(default=False, description="是否跳过土样检测")
):
    """
    统一剖面处理接口（精简版） - 完整的地质钻探剖面图三阶段处理流程

    处理流程：
    1. 第一阶段：调用 section_detection_routes API 检测输入剖面图中的多个剖面区域
    2. 第二阶段：对每个检测到的剖面区域，调用 section_detail_routes API 进行详细识别，识别三个类别：['剖面编号', '钻孔', '间距']
    3. 第三阶段：调用土样检测相关API（包括图像分割和土样识别）进行土样处理

    数据计算逻辑：
    - 基于第二阶段获得的钻孔信息、间距信息和深度-高程数据对，为每个土样计算真实深度值
    - 使用钻孔中的深度-高程数据对进行插值计算，得到每个土样的精确高程值

    Returns:
        精简的结构化JSON数据，每个土样对象包含以下字段：
        - bbox: [x1, y1, x2, y2] - 土样边界框坐标
        - depth: float - 计算得出的真实深度值
        - elevation: float - 计算得出的真实高程值
        - sample_type: string - 通过图像识别得到的具体土样类型名称
    """

    # 验证文件
    if not validate_image_file(file, Config.ALLOWED_EXTENSIONS):
        raise HTTPException(status_code=400, detail="不支持的文件格式")

    file_id = str(uuid.uuid4())
    temp_files = []

    try:
        # 保存上传的文件
        file_ext = Path(file.filename).suffix.lower()
        input_path = os.path.join(Config.UPLOAD_DIR, f"{file_id}_input{file_ext}")
        await save_upload_file(file, input_path, Config.MAX_FILE_SIZE)
        temp_files.append(input_path)

        # 调用统一流程处理方法
        result = process_unified_section(
            image_input=input_path,
            file_id=file_id,
            filename=file.filename,
            detection_confidence=detection_confidence,
            detection_iou=detection_iou,
            detail_confidence=detail_confidence,
            detail_iou=detail_iou,
            detail_model_type=detail_model_type,
            ocr_model=ocr_model,  # 传递OCR模型参数
            enable_soil_sample_detection=enable_soil_sample_detection,
            soil_detection_confidence=soil_detection_confidence,
            soil_detection_iou=soil_detection_iou,
            enable_slice=enable_slice,
            slice_width=slice_width,
            slice_height=slice_height,
            overlap_ratio=overlap_ratio,
            nms_threshold=nms_threshold,
            return_image=return_image,
            skip_detail_recognition=skip_detail_recognition,
            skip_soil_sample_detection=skip_soil_sample_detection,
            save_json=True  
        )

        # 创建精简结果
        simplified_result = create_simplified_result(result)

        # 添加后台任务清理临时文件
        def cleanup_after_delay():
            import time
            time.sleep(300)  # 5分钟后清理
            cleanup_temp_files(temp_files + result.get("temp_files", []))

        background_tasks.add_task(cleanup_after_delay)

        logger.info(f"精简统一处理完成: {file.filename}")
        logger.info(f"结果: {simplified_result.summary['total_sections']}个剖面, {simplified_result.summary['total_drill_holes']}个钻孔, {simplified_result.summary['total_soil_samples']}个土样")

        return simplified_result

    except HTTPException:
        cleanup_temp_files(temp_files)
        raise
    except Exception as e:
        cleanup_temp_files(temp_files)
        logger.error(f"统一处理过程中发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")


def _assign_spacing_to_drill_holes(drill_holes: List[Dict], spacings: List[Dict]) -> List[Dict]:
    """
    为钻孔分配间距信息
    按x轴坐标排序，最后一个钻孔作为终孔不需要间距

    Args:
        drill_holes: 钻孔列表
        spacings: 间距信息列表

    Returns:
        带有间距分配信息的钻孔列表
    """
    if not drill_holes:
        return drill_holes

    # 按x轴坐标排序钻孔
    sorted_drill_holes = sorted(drill_holes, key=lambda hole: hole.get("bbox", {}).get("x", 0))

    # 为每个钻孔分配间距（除了最后一个）
    for i, drill_hole in enumerate(sorted_drill_holes):
        drill_hole["x_coordinate"] = drill_hole.get("bbox", {}).get("x", 0)
        drill_hole["is_terminal_hole"] = (i == len(sorted_drill_holes) - 1)

        if not drill_hole["is_terminal_hole"] and i < len(spacings):
            # 为非终孔分配间距
            drill_hole["assigned_spacing"] = spacings[i] if i < len(spacings) else None
            logger.info(f"钻孔 {drill_hole.get('hole_id', 'Unknown')} 分配间距: {spacings[i] if i < len(spacings) else 'None'}")
        else:
            drill_hole["assigned_spacing"] = None
            if drill_hole["is_terminal_hole"]:
                logger.info(f"钻孔 {drill_hole.get('hole_id', 'Unknown')} 为终孔，不分配间距")

    return sorted_drill_holes

def _process_soil_samples_with_integration(soil_detections: List[Dict],
                                         drill_holes_with_spacing: List[Dict],
                                         soil_processor) -> Tuple[List[Dict], List[Dict]]:
    """
    使用section_integration处理器处理土样数据

    Args:
        soil_detections: 土样检测结果
        drill_holes_with_spacing: 带有间距分配的钻孔列表
        soil_processor: 土样处理器实例

    Returns:
        tuple: (处理后的钻孔列表, 未分配的土样列表)
    """
    processed_drill_holes = []
    unassigned_soil_detections = soil_detections.copy()

    logger.info(f"开始处理土样数据: {len(soil_detections)} 个土样检测结果, {len(drill_holes_with_spacing)} 个钻孔")

    for drill_hole in drill_holes_with_spacing:
        hole_id = drill_hole.get("hole_id", "Unknown")
        surface_elevation = drill_hole.get("surface_elevation", 85.0)
        drill_hole_bbox = drill_hole.get("bbox", {})
        assigned_spacing = drill_hole.get("assigned_spacing")

        logger.info(f"处理钻孔: {hole_id}, 孔口标高: {surface_elevation}m, bbox: {drill_hole_bbox}")

        # 提取钻孔的深度-高程数据对
        drill_hole_depth_elevation_pairs = []
        if "depth_elevation_pairs" in drill_hole:
            for pair in drill_hole["depth_elevation_pairs"]:
                if hasattr(pair, 'dict'):
                    drill_hole_depth_elevation_pairs.append(pair.dict())
                else:
                    drill_hole_depth_elevation_pairs.append(pair)

        logger.info(f"钻孔 {hole_id} 深度-高程数据对: {len(drill_hole_depth_elevation_pairs)} 个")

        # 筛选属于该钻孔的土样
        drill_hole_detections = _filter_soil_samples_for_drill_hole(
            unassigned_soil_detections, drill_hole_bbox
        )

        logger.info(f"钻孔 {hole_id} 分配到的土样: {len(drill_hole_detections)} 个")

        # 无论是否有土样检测结果，都要创建钻孔对象
        drill_hole_samples = []

        if drill_hole_detections:
            # 使用整合后的土样处理方法
            drill_hole_samples = soil_processor.process_soil_sample_detections(
                detections=drill_hole_detections,
                drill_hole_id=hole_id,
                surface_elevation=surface_elevation,
                spacings=[assigned_spacing] if assigned_spacing else [],
                depth_elevation_pairs=drill_hole_depth_elevation_pairs
            )

        # 转换为简化格式
        simplified_samples = []
        for sample in drill_hole_samples:
            if hasattr(sample, 'dict'):
                sample_dict = sample.dict()
            else:
                sample_dict = sample

            simplified_sample = SoilSampleInfo(
                sample_id=sample_dict.get("sample_id", ""),
                depth=sample_dict.get("depth", 0.0),
                elevation=sample_dict.get("elevation", 0.0),
                sample_type=sample_dict.get("sample_type", "未知类型"),
                bbox=sample_dict.get("bbox"),
                bbox_relative=sample_dict.get("bbox_relative"),
                confidence=sample_dict.get("confidence")
            )
            simplified_samples.append(simplified_sample)

        # 从未分配列表中移除已分配的土样
        for detection in drill_hole_detections:
            if detection in unassigned_soil_detections:
                unassigned_soil_detections.remove(detection)

        # 创建简化的钻孔数据（无论是否有土样都要创建）
        simplified_drill_hole = SimpleDrillHole(
            hole_id=hole_id,
            surface_elevation=surface_elevation,
            bbox=drill_hole_bbox,
            bbox_relative=drill_hole.get("bbox_relative"),
            x_coordinate=drill_hole.get("x_coordinate"),
            assigned_spacing=assigned_spacing,
            is_terminal_hole=drill_hole.get("is_terminal_hole", False),
            samples=simplified_samples
        )

        processed_drill_holes.append(simplified_drill_hole)

        logger.info(f"钻孔 {hole_id} 处理完成: {len(simplified_samples)} 个土样")

    logger.info(f"土样处理完成: 总共处理了 {len(processed_drill_holes)} 个钻孔")
    return processed_drill_holes, unassigned_soil_detections

def _filter_soil_samples_for_drill_hole(soil_detections: List[Dict],
                                       drill_hole_bbox: Dict) -> List[Dict]:
    """
    筛选属于特定钻孔的土样

    Args:
        soil_detections: 土样检测结果
        drill_hole_bbox: 钻孔边界框

    Returns:
        属于该钻孔的土样列表
    """
    if not drill_hole_bbox or not soil_detections:
        return []

    drill_x = drill_hole_bbox["x"]
    drill_y = drill_hole_bbox["y"]
    drill_width = drill_hole_bbox["width"]
    drill_height = drill_hole_bbox["height"]
    drill_x_center = drill_x + drill_width / 2
    drill_y_center = drill_y + drill_height / 2

    filtered_samples = []
    for detection in soil_detections:
        soil_bbox = detection.get("bbox", {})
        if not soil_bbox:
            continue

        soil_x = soil_bbox["x"]
        soil_y = soil_bbox["y"]
        soil_width = soil_bbox["width"]
        soil_height = soil_bbox["height"]
        soil_x_center = soil_x + soil_width / 2
        soil_y_center = soil_y + soil_height / 2

        # 使用距离和位置关系判断土样是否属于该钻孔
        x_distance = abs(soil_x_center - drill_x_center)
        y_distance = abs(soil_y_center - drill_y_center)

        # 土样应该在钻孔的垂直方向下方，且水平距离不能太远
        if (soil_y > drill_y and  # 土样在钻孔下方
            x_distance < drill_width * 2 and  # 水平距离合理
            y_distance < drill_height * 10):  # 垂直距离合理
            filtered_samples.append(detection)

    return filtered_samples
