#!/bin/bash

# 地质剖面报表生成API批量处理测试脚本

BASE_URL="http://localhost:8080"

echo "=== 地质剖面报表生成API批量处理测试 ==="
echo

# 1. 健康检查
echo "1. 测试健康检查接口..."
curl -s -X GET "${BASE_URL}/api/simple-report/health" | jq '.'
echo

# 2. 获取接口信息
echo "2. 获取接口使用说明..."
curl -s -X GET "${BASE_URL}/api/simple-report/info" | jq '.'
echo

# 3. 测试批量报表生成
if [ "$#" -gt 0 ]; then
    echo "3. 测试批量报表生成..."
    echo "上传文件数量: $#"
    echo "文件列表: $@"
    
    # 检查所有文件是否存在
    for file in "$@"; do
        if [ ! -f "$file" ]; then
            echo "错误: 文件 $file 不存在"
            exit 1
        fi
    done
    
    # 构建curl命令
    curl_cmd="curl -s -X POST"
    for file in "$@"; do
        curl_cmd="$curl_cmd -F \"files=@$file\""
    done
    curl_cmd="$curl_cmd \"${BASE_URL}/api/simple-report/generate\""
    
    echo "正在批量生成报表，请稍候..."
    echo "执行命令: $curl_cmd"
    
    # 执行请求
    response=$(eval $curl_cmd)
    
    echo "响应结果:"
    echo "$response" | jq '.'
    
    # 检查是否成功
    success=$(echo "$response" | jq -r '.success')
    if [ "$success" = "true" ]; then
        report_url=$(echo "$response" | jq -r '.report_url')
        file_count=$(echo "$response" | jq -r '.file_count')
        filenames=$(echo "$response" | jq -r '.filenames')
        echo
        echo "✅ 批量报表生成成功！"
        echo "📊 处理文件数: $file_count"
        echo "📁 文件名: $filenames"
        echo "📊 下载链接: $report_url"
    else
        message=$(echo "$response" | jq -r '.message')
        echo
        echo "❌ 批量报表生成失败: $message"
    fi
else
    echo "3. 跳过批量报表生成测试（未提供图片文件）"
    echo "   使用方法: $0 <图片文件1> <图片文件2> ..."
    echo "   示例: $0 /path/to/image1.jpg /path/to/image2.png /path/to/image3.jpg"
    echo
    echo "4. 测试单文件接口（兼容性测试）..."
    echo "   使用方法: curl -X POST -F \"file=@image.jpg\" ${BASE_URL}/api/simple-report/generate-single"
fi

echo
echo "=== 测试完成 ==="

# 显示使用提示
echo
echo "💡 使用提示:"
echo "1. 批量处理: $0 file1.jpg file2.png file3.jpg"
echo "2. 单文件处理: curl -X POST -F \"file=@image.jpg\" ${BASE_URL}/api/simple-report/generate-single"
echo "3. 多文件处理: curl -X POST -F \"files=@img1.jpg\" -F \"files=@img2.png\" ${BASE_URL}/api/simple-report/generate"
echo "4. 查看接口信息: curl ${BASE_URL}/api/simple-report/info"
